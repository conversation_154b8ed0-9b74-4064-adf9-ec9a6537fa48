# BuddyChip Development Plans

## Subscription System Restructure Plan

### ✅ Analysis Complete
- [x] Analyzed current database state
- [x] Identified two team plans to merge
- [x] Found pricing inconsistencies between code and database
- [x] Reviewed current UI components and billing system

### ✅ Database Changes Complete
- [x] Removed duplicate team plan ($299 one)
- [x] Added free tier plan and features
- [x] Updated team plan to unified structure
- [x] Updated all plan features with correct limits
- [x] Verified database state is correct

### ✅ New Components Created
- [x] `apps/web/src/components/upgrade-prompt.tsx` - Smart upgrade prompts
- [x] `apps/web/src/components/feature-gate.tsx` - Feature limiting with upgrade options
- [x] `apps/web/src/components/free-tier-onboarding.tsx` - Welcome experience for free users
- [x] `test/subscription-test.ts` - Comprehensive subscription system test

### 🎯 Goals
1. Merge two team plans into one comprehensive team plan
2. Add a free tier to attract new users
3. Create logical feature progression across tiers
4. Ensure consistency between database, code, and UI

### 📊 New Subscription Tier Structure

#### Free Tier (New)
- **Price**: $0/month
- **Target**: New users trying the platform
- **Features**:
  - 50 AI calls/month
  - 5 image generations/month
  - 1 monitored account
  - 100 mentions/month
  - 25 mentions per sync
  - 50 max total mentions
  - 0.5GB storage
  - 1 team member
  - 10 Cookie API calls/month
  - Basic support (community)

#### Reply Guy - $9/month
- **Target**: Individual users getting started
- **Features**:
  - 1,000 AI calls/month
  - 20 image generations/month
  - 3 monitored accounts
  - 1,000 mentions/month
  - 25 mentions per sync
  - 100 max total mentions
  - 1GB storage
  - 1 team member
  - 50 Cookie API calls/month
  - Email support

#### Reply God - $29/month
- **Target**: Power users and small teams
- **Features**:
  - 5,000 AI calls/month
  - 100 image generations/month
  - 10 monitored accounts
  - 5,000 mentions/month
  - 100 mentions per sync
  - 500 max total mentions
  - 5GB storage
  - 3 team members
  - 200 Cookie API calls/month
  - Priority support
  - Custom personas

#### Team - $99/month
- **Target**: Teams and organizations
- **Features**:
  - Unlimited AI calls
  - Unlimited image generations
  - 50 monitored accounts
  - Unlimited mentions/month
  - 200 mentions per sync
  - 2,000 max total mentions
  - 20GB storage
  - 10 team members
  - Unlimited Cookie API calls
  - Dedicated support
  - Team collaboration
  - Admin dashboard
  - Custom integrations

---

## Telegram Bot Integration Plan

### Overview
Implement a full-featured Telegram bot that provides complete access to the Benji AI agent capabilities, including Twitter link processing and all existing tools.

### Phase 1: Core Infrastructure ✅
- [x] Plan created
- [x] Install Telegram bot dependencies
- [x] Create core Telegram bot service
- [x] Update database schema for Telegram users
- [x] Create authentication system

### Phase 2: API Routes & Webhooks ✅
- [x] Create Telegram webhook endpoint
- [x] Create authentication API routes
- [x] Extend tRPC router with Telegram procedures
- [x] Set up webhook with Telegram API

### Phase 3: Benji Agent Integration ✅
- [x] Create TelegramBenjiAgent wrapper class
- [x] Implement message formatting for Telegram constraints
- [x] Add conversation context management
- [x] Integrate all existing tools (xAI, Exa, image generation)

### Phase 4: Twitter Link Processing ✅
- [x] Implement Twitter URL detection in messages
- [x] Integrate with existing quick reply functionality
- [x] Format Twitter responses for Telegram
- [x] Add enhanced response support

### Phase 5: User Experience ✅
- [x] Implement command system (/start, /help, /settings, /status)
- [x] Add conversation management
- [x] Integrate rate limiting
- [x] Add user preferences and model selection

### Phase 6: Testing & Deployment ✅
- [x] Create comprehensive tests
- [x] Create deployment documentation
- [x] Create configuration checker script
- [x] Ready for webhook setup on buddychip.app domain
- [x] Ready for deployment and user testing

### Technical Requirements
- Domain: buddychip.app for webhook endpoints
- All existing Benji tools accessible via Telegram
- Twitter link processing identical to dashboard quick reply
- User authentication and account linking
- Rate limiting and subscription plan integration
- Conversation state management

### Key Features
1. **Full Benji Agent Access**: All tools and capabilities available
2. **Twitter Integration**: Send Twitter links for instant AI replies
3. **Multi-turn Conversations**: Context-aware discussions
4. **User Management**: Account linking and preferences
5. **Rate Limiting**: Subscription plan enforcement
6. **Rich Responses**: Text, images, and formatted content

---

## Enhanced Notepad System Implementation Plan

### Overview
Transform the "Enhance" feature into a comprehensive notepad system for crafting Twitter responses with research tools, source management, and draft versioning.

### Implementation Progress

#### ✅ Completed Tasks

##### 1. Database Schema Design
- [x] Created `MentionNotepad` table for notepad sessions
- [x] Created `NotepadSource` table for research sources and citations
- [x] Created `NotepadDraft` table for draft versions
- [x] Added relationships to existing User and Mention models
- [x] Generated Prisma client with new schema
- [x] Created database migration script (002_notepad_system.sql)

##### 2. Backend API Development
- [x] Created notepad router (`/src/routers/notepad.ts`)
- [x] Implemented `getOrCreate` endpoint for notepad sessions
- [x] Implemented `update` endpoint for notepad content
- [x] Implemented `addSource` endpoint for source management
- [x] Implemented `updateSource` endpoint for bookmarks/ratings
- [x] Implemented `deleteSource` endpoint
- [x] Implemented `createDraft` endpoint for draft management
- [x] Added notepad router to main tRPC router

##### 3. Enhanced Research Tools
- [x] Created notepad-integrated search tools (`/src/lib/tools/notepad-research.ts`)
- [x] Extended XAI search tool with automatic source saving
- [x] Extended Exa search tool with automatic source saving
- [x] Added source extraction and metadata processing
- [x] Implemented research session tracking

##### 4. Frontend Components
- [x] Created comprehensive NotepadModal component (`/src/components/ui/notepad-modal.tsx`)
- [x] Implemented tabbed interface (Overview, Research, Drafts, Notes)
- [x] Added source management UI with bookmarking and rating
- [x] Added draft management with versioning
- [x] Created Tabs UI component using Radix UI
- [x] Installed required dependencies (@radix-ui/react-tabs)

##### 5. Integration with Reply Guy Page
- [x] Added notepad button to mention cards (mobile and desktop layouts)
- [x] Implemented notepad modal state management
- [x] Added handlers for opening/closing notepad
- [x] Integrated notepad modal with existing UI

#### ✅ Recently Completed Tasks

##### 6. Database Migration & Testing
- [x] Run database migration in development environment
- [x] Created all notepad tables with proper structure
- [x] Added foreign key constraints and cascading deletes
- [x] Created indexes for optimal performance
- [x] Added check constraints for data validation
- [x] Created triggers for automatic timestamp updates
- [x] Verified all tables exist and have correct structure

##### 7. TypeScript Compilation & Build Fixes
- [x] Fixed import errors in notepad router
- [x] Fixed TypeScript errors in notepad modal
- [x] Updated error handling with proper typing
- [x] Verified TypeScript compilation passes
- [x] Fixed tool execution context issues

#### 🔄 Current Progress: Merge Resolution
- [x] Resolved merge conflicts in reply-guy page.tsx
- [x] Resolved merge conflicts in tRPC router index.ts
- [x] Resolved merge conflicts in plan.md
- [ ] Complete final merge and test functionality

#### 📋 Remaining Tasks

##### 8. Integration Testing
- [ ] Test notepad creation and basic operations
- [ ] Test source management (add, update, delete)
- [ ] Test draft creation and versioning
- [ ] Verify integration with existing enhance functionality

##### 9. Enhanced Search Integration
- [ ] Integrate notepad research tools with Benji agent
- [ ] Update enhance functionality to use notepad-aware tools
- [ ] Add automatic source saving during enhance operations
- [ ] Implement research session context preservation

##### 10. Advanced Features
- [ ] Add real-time search functionality in notepad
- [ ] Implement draft comparison and merging
- [ ] Add export functionality for notepad content
- [ ] Create source citation formatting
- [ ] Add collaborative features for team plans

### Technical Architecture

#### Database Design
```
MentionNotepad (1:1 with Mention)
├── NotepadSource (1:many)
└── NotepadDraft (1:many)
```

#### API Endpoints
- `notepad.getOrCreate` - Get or create notepad for mention
- `notepad.update` - Update notepad content
- `notepad.addSource` - Add research source
- `notepad.updateSource` - Update source metadata
- `notepad.deleteSource` - Remove source
- `notepad.createDraft` - Create new draft version

#### Frontend Components
- `NotepadModal` - Main notepad interface
- `Tabs` - Tabbed navigation component
- Integration with existing Reply Guy page

#### Research Tools
- `notepadXaiSearchTool` - XAI search with auto-save
- `notepadExaSearchTool` - Exa search with auto-save
- Source extraction and metadata processing

### Next Steps

1. **Complete Merge Resolution**: Finish resolving all merge conflicts
2. **Test Basic Functionality**: Verify notepad creation and basic operations
3. **Integrate Enhanced Search**: Connect research tools with notepad system
4. **Add Real-time Features**: Implement live search and auto-save
5. **Polish UI/UX**: Improve loading states and user experience
6. **Performance Testing**: Ensure system scales with usage

### Success Metrics

- [ ] Users can create and manage notepad sessions
- [ ] Research tools automatically save sources
- [ ] Draft versioning works correctly
- [ ] Source management (bookmark, rate, delete) functions
- [ ] Integration with existing enhance feature
- [ ] Performance meets requirements (< 2s response times)
- [ ] Mobile responsiveness maintained
- [ ] No breaking changes to existing functionality

---

## Twitter Persona Generation Feature - Implementation Plan

### Overview
Implement a comprehensive feature that analyzes Twitter accounts (500 tweets + 500 replies) to generate AI personas that capture unique writing styles, personality traits, and communication patterns.

### ✅ Phase 1: Database Schema Updates
- [x] Extended PersonalityProfile model with user-generated persona fields
- [x] Added PersonaGenerationJob model for tracking generation progress
- [x] Added PERSONA_GENERATIONS to FeatureType enum
- [x] Updated User model with persona relationships
- [x] Generated updated Prisma client

### ✅ Phase 2: Core Services
- [x] Created TwitterPersonaService for data collection and orchestration
- [x] Extended BenjiAgent with persona analysis methods:
  - [x] analyzeWritingStyle() - tone, formality, humor analysis
  - [x] extractPersonalityTraits() - primary/secondary traits
  - [x] identifyTopicsOfInterest() - expertise and interest areas
  - [x] analyzeEngagementPatterns() - reply style and supportiveness
  - [x] generatePersonaSystemPrompt() - comprehensive prompt generation

### ✅ Phase 3: API Integration
- [x] Created persona tRPC router with endpoints:
  - [x] generateFromTwitter - start persona generation
  - [x] getJobStatus - track generation progress
  - [x] getUserJobs - list user's generation jobs
  - [x] getUserGeneratedPersonas - list generated personas
  - [x] deleteGeneratedPersona - remove personas
  - [x] getPersonaLimits - check subscription limits
- [x] Added persona router to main tRPC router
- [x] Updated seed script with persona generation limits per plan

### ✅ Phase 4: User Interface
- [x] Created PersonaGenerator component with:
  - [x] Twitter handle input and validation
  - [x] Real-time progress tracking
  - [x] Generated personas management
  - [x] Recent jobs history
  - [x] Usage limits display
- [x] Created dedicated persona generator page with:
  - [x] Feature explanation and how-it-works
  - [x] Tips for best results
  - [x] Integration with main component
- [x] Updated PersonalitySelector to include generated personas
- [x] Added persona generator link to profile page

### ✅ Phase 5: Testing & Polish
- [x] Fixed all TypeScript compilation errors
- [x] Created missing UI components (Alert)
- [x] Resolved import path issues
- [x] Fixed tRPC query type issues
- [ ] Test persona generation with various Twitter accounts (requires database setup)
- [ ] Verify subscription limits enforcement (requires database setup)
- [ ] Test error handling and recovery (requires database setup)
- [ ] Validate AI analysis quality (requires API keys)
- [ ] Test UI responsiveness and user experience

### 📋 Subscription Plan Limits
- **Free Plan**: 0 persona generations
- **Reply Guy**: 1 persona generation per month
- **Reply God**: 3 persona generations per month
- **Team Plan**: Unlimited persona generations

### 🎯 Key Features Implemented

#### 🧠 Advanced AI Analysis
- **Writing Style Analysis**: Tone, formality, humor, emotional range, vocabulary
- **Personality Traits**: Primary traits, secondary traits, communication style
- **Topics of Interest**: Primary topics, secondary topics, expertise areas
- **Engagement Patterns**: Reply style, question asking frequency, supportiveness

#### 🔄 Robust Data Collection
- Fetches up to 500 tweets and 500 replies with pagination
- Handles Twitter API rate limits and errors
- Real-time progress tracking and status updates
- Comprehensive error handling and recovery

#### 🎭 Smart Persona Generation
- AI-generated comprehensive system prompts
- Authentic voice replication based on analysis
- Context-aware personality profiles
- Ready-to-use in existing personality system

#### 🎨 User Experience
- Intuitive persona generation interface
- Real-time progress tracking with detailed status
- Generated persona management (view, delete)
- Seamless integration with existing personality selector
- Clear usage limits and subscription information
