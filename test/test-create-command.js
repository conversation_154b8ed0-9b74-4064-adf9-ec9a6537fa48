/**
 * Test the new /create command functionality
 */

const { getTelegramBenjiForUser } = require('../apps/web/src/lib/telegram-benji-agent');

async function testCreateCommand() {
  console.log('🧪 Testing /create command functionality...');

  try {
    // Test with a sample user ID (you'll need to replace with a real user ID)
    const testUserId = 'test-user-id';
    const testTelegramUserId = 'test-telegram-user-id';
    const testChatId = 'test-chat-id';

    console.log('📝 Testing post generation...');

    // Create a Telegram Benji agent
    const benji = await getTelegramBenjiForUser(
      testUserId,
      testTelegramUserId,
      testChatId
    );

    // Test post generation with a sample prompt
    const testPrompt = "Write a motivational post about learning new skills in 2025";
    
    console.log(`🎯 Generating post for prompt: "${testPrompt}"`);

    const result = await benji.generateTelegramPost(testPrompt, {
      telegramUserId: testTelegramUserId,
      telegramChatId: testChatId,
    });

    // Convert streaming result to text
    let postContent = "";
    for await (const chunk of result.textStream) {
      postContent += chunk;
    }

    console.log('✅ Post generated successfully!');
    console.log('📄 Generated content:');
    console.log('=' .repeat(50));
    console.log(postContent);
    console.log('=' .repeat(50));

    // Test character count
    console.log(`📊 Character count: ${postContent.length}/280`);
    
    if (postContent.length > 280) {
      console.warn('⚠️  Warning: Post exceeds Twitter character limit');
    } else {
      console.log('✅ Post is within Twitter character limit');
    }

    // Test that it doesn't contain meta-commentary
    const metaCommentaryPhrases = [
      'Here\'s a post',
      'This post',
      'I\'ve created',
      'Here\'s your',
      'This motivational post'
    ];

    const hasMetaCommentary = metaCommentaryPhrases.some(phrase => 
      postContent.toLowerCase().includes(phrase.toLowerCase())
    );

    if (hasMetaCommentary) {
      console.warn('⚠️  Warning: Post contains meta-commentary');
    } else {
      console.log('✅ Post is clean (no meta-commentary)');
    }

    console.log('🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    
    if (error.message.includes('User not found')) {
      console.log('💡 Note: This test requires a valid user ID. Update the testUserId variable with a real user ID from your database.');
    }
  }
}

// Run the test
if (require.main === module) {
  testCreateCommand().catch(console.error);
}

module.exports = { testCreateCommand };
