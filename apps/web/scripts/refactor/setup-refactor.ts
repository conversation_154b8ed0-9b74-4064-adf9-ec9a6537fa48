#!/usr/bin/env tsx
/**
 * Refactoring Setup Script
 * 
 * Sets up the refactoring environment with necessary tools,
 * configurations, and baseline measurements.
 */

import { execSync } from "child_process";
import { writeFileSync, mkdirSync, existsSync } from "fs";
import { join } from "path";

interface RefactorConfig {
  version: string;
  startDate: string;
  baseline: {
    fileCount: number;
    totalLines: number;
    largeFiles: string[];
    testCoverage: number;
    bundleSize: number;
  };
  targets: {
    maxFileLines: number;
    minTestCoverage: number;
    maxBundleSize: number;
    codeReduction: number;
  };
  phases: Array<{
    name: string;
    description: string;
    duration: string;
    dependencies: string[];
  }>;
}

class RefactorSetup {
  private configPath = join(process.cwd(), "refactor.config.json");
  private scriptsDir = join(process.cwd(), "scripts", "refactor");
  
  /**
   * Initialize refactoring environment
   */
  async setup(): Promise<void> {
    console.log("🚀 Setting up refactoring environment...\n");
    
    try {
      // Create directories
      await this.createDirectories();
      
      // Install required tools
      await this.installTools();
      
      // Create configuration
      await this.createConfiguration();
      
      // Setup package.json scripts
      await this.setupScripts();
      
      // Create baseline measurements
      await this.createBaseline();
      
      // Setup feature flags
      await this.setupFeatureFlags();
      
      // Create documentation
      await this.createDocumentation();
      
      console.log("✅ Refactoring environment setup complete!\n");
      console.log("📋 Next steps:");
      console.log("   1. Run 'pnpm refactor:validate' to check current state");
      console.log("   2. Run 'pnpm refactor:quality' for quality analysis");
      console.log("   3. Review refactor.config.json for targets");
      console.log("   4. Start with Phase 1: Foundation & Setup");
      
    } catch (error) {
      console.error("❌ Setup failed:", error);
      throw error;
    }
  }
  
  /**
   * Create necessary directories
   */
  private async createDirectories(): Promise<void> {
    console.log("📁 Creating directories...");
    
    const directories = [
      this.scriptsDir,
      join(process.cwd(), "src", "lib", "utils"),
      join(process.cwd(), "src", "lib", "middleware"),
      join(process.cwd(), "src", "components", "ui", "unified"),
      join(process.cwd(), "test", "refactor"),
      join(process.cwd(), "docs", "refactor"),
    ];
    
    for (const dir of directories) {
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
        console.log(`   Created: ${dir}`);
      }
    }
  }
  
  /**
   * Install required development tools
   */
  private async installTools(): Promise<void> {
    console.log("🔧 Installing refactoring tools...");
    
    const tools = [
      "madge", // Circular dependency detection
      "jscpd", // Code duplication detection
      "unimported", // Dead code detection
      "depcheck", // Unused dependency detection
      "typescript-complexity", // Complexity analysis
      "@typescript-eslint/parser",
      "@typescript-eslint/eslint-plugin",
    ];
    
    try {
      console.log("   Installing tools...");
      execSync(`pnpm add -D ${tools.join(" ")}`, {
        stdio: "inherit",
        cwd: process.cwd(),
      });
      console.log("   ✅ Tools installed successfully");
    } catch (error) {
      console.log("   ⚠️ Some tools may not be available - continuing anyway");
    }
  }
  
  /**
   * Create refactoring configuration
   */
  private async createConfiguration(): Promise<void> {
    console.log("⚙️ Creating configuration...");
    
    const config: RefactorConfig = {
      version: "1.0.0",
      startDate: new Date().toISOString(),
      baseline: {
        fileCount: 0,
        totalLines: 0,
        largeFiles: [],
        testCoverage: 0,
        bundleSize: 0,
      },
      targets: {
        maxFileLines: 500,
        minTestCoverage: 80,
        maxBundleSize: 50, // MB
        codeReduction: 30, // percentage
      },
      phases: [
        {
          name: "Phase 1: Foundation & Setup",
          description: "Establish refactoring infrastructure, utilities, and testing framework",
          duration: "1-2 weeks",
          dependencies: [],
        },
        {
          name: "Phase 2: Component Library Unification",
          description: "Unify button systems, standardize modals, and create reusable form components",
          duration: "1-2 weeks",
          dependencies: ["Phase 1"],
        },
        {
          name: "Phase 3: tRPC Architecture Improvements",
          description: "Extract middleware, consolidate routers, and create common schemas",
          duration: "2-3 weeks",
          dependencies: ["Phase 1"],
        },
        {
          name: "Phase 4: Database Optimization",
          description: "Optimize indexes, implement batch operations, and add query optimization services",
          duration: "1-2 weeks",
          dependencies: ["Phase 3"],
        },
        {
          name: "Phase 5: Core Module Decomposition",
          description: "Break down large files into focused modules",
          duration: "2-3 weeks",
          dependencies: ["Phase 2", "Phase 3"],
        },
        {
          name: "Phase 6: Benji Agent Migration",
          description: "Complete migration to modular Benji agent with comprehensive testing",
          duration: "2-3 weeks",
          dependencies: ["Phase 5"],
        },
      ],
    };
    
    writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    console.log(`   Configuration saved to: ${this.configPath}`);
  }
  
  /**
   * Setup package.json scripts
   */
  private async setupScripts(): Promise<void> {
    console.log("📜 Setting up scripts...");
    
    const packageJsonPath = join(process.cwd(), "package.json");
    const packageJson = JSON.parse(require("fs").readFileSync(packageJsonPath, "utf-8"));
    
    const refactorScripts = {
      "refactor:validate": "tsx scripts/refactor/validate-refactor.ts",
      "refactor:quality": "tsx scripts/refactor/quality-check.ts",
      "refactor:setup": "tsx scripts/refactor/setup-refactor.ts",
      "refactor:baseline": "tsx scripts/refactor/create-baseline.ts",
      "analyze:complexity": "madge --circular --warning src && typescript-complexity src",
      "analyze:duplication": "jscpd src --threshold 10",
      "analyze:dead-code": "unimported",
      "analyze:dependencies": "depcheck",
      "analyze:bundle": "pnpm build && du -sh .next",
      "check:all": "pnpm check-types && pnpm lint && pnpm test:unit:run",
    };
    
    packageJson.scripts = { ...packageJson.scripts, ...refactorScripts };
    
    writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log("   Scripts added to package.json");
  }
  
  /**
   * Create baseline measurements
   */
  private async createBaseline(): Promise<void> {
    console.log("📊 Creating baseline measurements...");
    
    try {
      // Count files and lines
      const fileStats = await this.getFileStats();
      
      // Get test coverage
      let testCoverage = 0;
      try {
        const coverageOutput = execSync("pnpm test:coverage --reporter=json", {
          encoding: "utf-8",
          cwd: process.cwd(),
        });
        const coverage = JSON.parse(coverageOutput);
        testCoverage = coverage.total?.lines?.pct || 0;
      } catch {
        console.log("   ⚠️ Could not measure test coverage");
      }
      
      // Get bundle size
      let bundleSize = 0;
      try {
        execSync("pnpm build", { stdio: "ignore", cwd: process.cwd() });
        const sizeOutput = execSync("du -sm .next", {
          encoding: "utf-8",
          cwd: process.cwd(),
        });
        bundleSize = parseInt(sizeOutput.split("\t")[0]) || 0;
      } catch {
        console.log("   ⚠️ Could not measure bundle size");
      }
      
      const baseline = {
        timestamp: new Date().toISOString(),
        fileCount: fileStats.fileCount,
        totalLines: fileStats.totalLines,
        largeFiles: fileStats.largeFiles,
        testCoverage,
        bundleSize,
      };
      
      const baselinePath = join(process.cwd(), "refactor-baseline.json");
      writeFileSync(baselinePath, JSON.stringify(baseline, null, 2));
      
      console.log(`   Baseline saved to: ${baselinePath}`);
      console.log(`   Files: ${baseline.fileCount}`);
      console.log(`   Total lines: ${baseline.totalLines}`);
      console.log(`   Large files: ${baseline.largeFiles.length}`);
      console.log(`   Test coverage: ${baseline.testCoverage}%`);
      console.log(`   Bundle size: ${baseline.bundleSize}MB`);
      
    } catch (error) {
      console.log("   ⚠️ Could not create complete baseline");
    }
  }
  
  /**
   * Setup feature flags for gradual rollout
   */
  private async setupFeatureFlags(): Promise<void> {
    console.log("🎛️ Setting up feature flags...");
    
    const envExamplePath = join(process.cwd(), ".env.example");
    const featureFlags = `
# Refactoring Feature Flags
USE_NEW_UTILITY_LIBRARY=false
USE_UNIFIED_COMPONENTS=false
USE_NEW_TRPC_MIDDLEWARE=false
USE_MODULAR_MENTIONS=false
USE_NEW_BENJI_AGENT=false
USE_OPTIMIZED_QUERIES=false

# Development Flags
ENABLE_REFACTOR_LOGGING=false
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_QUALITY_CHECKS=false
`;
    
    try {
      const existingEnv = require("fs").readFileSync(envExamplePath, "utf-8");
      if (!existingEnv.includes("Refactoring Feature Flags")) {
        require("fs").appendFileSync(envExamplePath, featureFlags);
        console.log("   Feature flags added to .env.example");
      }
    } catch {
      writeFileSync(envExamplePath, featureFlags);
      console.log("   Created .env.example with feature flags");
    }
  }
  
  /**
   * Create refactoring documentation
   */
  private async createDocumentation(): Promise<void> {
    console.log("📚 Creating documentation...");
    
    const readmePath = join(process.cwd(), "docs", "refactor", "README.md");
    const readme = `# BuddyChip Refactoring Guide

## Overview

This directory contains documentation for the comprehensive refactoring of BuddyChip.

## Quick Start

\`\`\`bash
# Validate current state
pnpm refactor:validate

# Run quality analysis
pnpm refactor:quality

# Check all metrics
pnpm check:all
\`\`\`

## Phases

1. **Foundation & Setup** - Utility libraries and infrastructure
2. **Component Unification** - Standardize UI components
3. **tRPC Improvements** - Middleware and router consolidation
4. **Database Optimization** - Query optimization and indexing
5. **Module Decomposition** - Break down large files
6. **Benji Migration** - Complete agent refactoring

## Scripts

- \`pnpm refactor:validate\` - Comprehensive validation
- \`pnpm refactor:quality\` - Code quality analysis
- \`pnpm analyze:complexity\` - Complexity analysis
- \`pnpm analyze:duplication\` - Code duplication check
- \`pnpm analyze:dead-code\` - Find unused code
- \`pnpm analyze:dependencies\` - Dependency analysis

## Configuration

See \`refactor.config.json\` for targets and phase definitions.

## Baseline

See \`refactor-baseline.json\` for initial measurements.
`;
    
    writeFileSync(readmePath, readme);
    console.log(`   Documentation created: ${readmePath}`);
  }
  
  /**
   * Get file statistics
   */
  private async getFileStats(): Promise<{
    fileCount: number;
    totalLines: number;
    largeFiles: string[];
  }> {
    try {
      const findOutput = execSync("find src -name '*.ts' -o -name '*.tsx' | wc -l", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      const fileCount = parseInt(findOutput.trim()) || 0;
      
      const linesOutput = execSync("find src -name '*.ts' -o -name '*.tsx' | xargs wc -l | tail -1", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      const totalLines = parseInt(linesOutput.trim().split(/\s+/)[0]) || 0;
      
      // Find large files
      const largeFilesOutput = execSync("find src -name '*.ts' -o -name '*.tsx' | xargs wc -l | awk '$1 > 500 {print $2}'", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      const largeFiles = largeFilesOutput
        .split("\n")
        .filter(line => line.trim())
        .filter(line => !line.includes("total"));
      
      return { fileCount, totalLines, largeFiles };
    } catch {
      return { fileCount: 0, totalLines: 0, largeFiles: [] };
    }
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new RefactorSetup();
  setup.setup().catch(error => {
    console.error("❌ Setup failed:", error);
    process.exit(1);
  });
}

export { RefactorSetup };
