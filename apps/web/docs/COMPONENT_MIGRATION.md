# Component Library Unification Migration Guide

This guide outlines the migration from the previous component system to the new unified component library.

## Overview

The unified component library consolidates:
- Button systems (atoms/button + ui/button + icon-button)
- Modal patterns (confirmation-dialog + notepad-modal + custom modals)
- Form components with validation
- Loading states and skeletons
- Consistent styling and behavior

## Migration Steps

### 1. Button Components

#### Before (Multiple Button Systems)
```tsx
// Old atoms/button
import PrimaryButton from "@/components/atoms/button";
import IconButton from "@/components/atoms/icon-button";
import { Button } from "@/components/ui/button";

// Usage
<PrimaryButton variant="secondary" onClick={handleClick}>
  Click me
</PrimaryButton>

<IconButton icon={SomeIcon} variant="primary" onClick={handleClick} />

<Button variant="default" size="lg">
  Regular Button
</Button>
```

#### After (Unified Button System)
```tsx
// New unified system
import { Button, IconButton, PrimaryButton } from "@/components/ui";

// Usage - Enhanced Button with loading states
<Button 
  variant="primary" 
  size="primary" 
  loading={isLoading}
  loadingText="Processing..."
  icon={<SomeIcon />}
  iconPosition="right"
  onClick={handleClick}
>
  Click me
</Button>

// IconButton now extends Button
<IconButton 
  icon={SomeIcon} 
  variant="appSecondary" 
  onClick={handleClick} 
/>

// PrimaryButton maintains compatibility
<PrimaryButton variant="primary" onClick={handleClick}>
  Primary Action
</PrimaryButton>
```

### 2. Modal Components

#### Before (Custom Modal Implementations)
```tsx
// Old confirmation dialog
import ConfirmationDialog from "@/components/ui/confirmation-dialog";

// Custom modal with manual backdrop/escape handling
const [isOpen, setIsOpen] = useState(false);

useEffect(() => {
  const handleEscape = (e: KeyboardEvent) => {
    if (e.key === "Escape") setIsOpen(false);
  };
  // ... manual event handling
}, [isOpen]);

return (
  <div className="fixed inset-0 z-50">
    <div className="backdrop" onClick={() => setIsOpen(false)} />
    <div className="modal-content">
      {/* Custom modal content */}
    </div>
  </div>
);
```

#### After (Unified Modal System)
```tsx
// New unified modals
import { Modal, ConfirmationModal, useModal } from "@/components/ui";

// Simple modal usage
<Modal
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  title="Modal Title"
  description="Modal description"
  size="lg"
  closeOnBackdrop={true}
  closeOnEscape={true}
>
  <div className="p-4">
    Modal content here
  </div>
</Modal>

// Confirmation modal
<ConfirmationModal
  isOpen={showConfirm}
  onClose={() => setShowConfirm(false)}
  onConfirm={handleConfirm}
  title="Confirm Action"
  message="Are you sure you want to proceed?"
  confirmVariant="destructive"
  showDontShowAgain={true}
/>

// Custom modal with hook
function CustomModal() {
  const [isOpen, setIsOpen] = useState(false);
  useModal(isOpen, () => setIsOpen(false), {
    closeOnEscape: true,
    preventBodyScroll: true
  });
  
  // Modal automatically handles escape key and body scroll
}
```

### 3. Form Components

#### Before (Manual Form Handling)
```tsx
// Old form pattern
const [values, setValues] = useState({});
const [errors, setErrors] = useState({});

const handleSubmit = (e) => {
  e.preventDefault();
  // Manual validation
  if (!values.email) {
    setErrors({ email: "Email is required" });
    return;
  }
  // Submit logic
};

return (
  <form onSubmit={handleSubmit}>
    <div>
      <label htmlFor="email">Email</label>
      <input
        id="email"
        value={values.email}
        onChange={(e) => setValues({ ...values, email: e.target.value })}
      />
      {errors.email && <span className="error">{errors.email}</span>}
    </div>
  </form>
);
```

#### After (Unified Form System)
```tsx
// New unified form system
import { 
  Form, 
  FormField, 
  FormLabel, 
  FormControl, 
  FormMessage,
  useForm,
  validateField 
} from "@/components/ui";

function MyForm() {
  const form = useForm({
    email: "",
    password: ""
  });

  const handleSubmit = () => {
    const isValid = form.validate({
      email: { required: true, pattern: /\S+@\S+\.\S+/ },
      password: { required: true, minLength: 8 }
    });
    
    if (isValid) {
      // Submit logic
      console.log(form.values);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      <FormField name="email" error={form.errors.email} required>
        <FormLabel>Email Address</FormLabel>
        <FormControl>
          <Input
            type="email"
            value={form.values.email}
            onChange={(e) => form.setValue("email", e.target.value)}
            placeholder="Enter your email"
          />
        </FormControl>
        <FormMessage />
      </FormField>

      <FormField name="password" error={form.errors.password} required>
        <FormLabel>Password</FormLabel>
        <FormControl>
          <Input
            type="password"
            value={form.values.password}
            onChange={(e) => form.setValue("password", e.target.value)}
          />
        </FormControl>
        <FormMessage />
      </FormField>

      <Button type="submit" loading={isSubmitting}>
        Submit
      </Button>
    </Form>
  );
}
```

### 4. Loading Components

#### Before (Multiple Loading Patterns)
```tsx
// Old loading patterns
import { Loader2 } from "lucide-react";
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation";
import Loader from "@/components/loader";

// Various loading implementations
<div className="flex items-center justify-center">
  <Loader2 className="animate-spin" />
</div>

<ShardLoadingAnimation size={64} />

<Loader />
```

#### After (Unified Loading System)
```tsx
// New unified loading system
import { 
  Loading, 
  PageLoading, 
  InlineLoading, 
  LoadingOverlay,
  SkeletonText,
  SkeletonCard 
} from "@/components/ui";

// Various loading states
<Loading variant="spinner" size="md" text="Loading..." />
<Loading variant="shard" size="lg" />
<Loading variant="dots" size="sm" />

// Specialized loading components
<PageLoading text="Loading application..." />
<InlineLoading text="Fetching data..." />

// Loading overlay
<LoadingOverlay isLoading={isLoading} text="Processing...">
  <div>Content that gets overlaid</div>
</LoadingOverlay>

// Skeleton loading
<SkeletonText lines={3} />
<SkeletonCard />
```

## Breaking Changes

### Button Variants
- `variant="primary"` in atoms/button now maps to unified `variant="primary"`
- IconButton `variant` values changed:
  - `"primary"` → `"appSecondary"`
  - `"secondary"` → `"appSecondary"`
  - `"tertiary"` → `"appTertiary"`

### Modal Props
- All modals now use consistent prop names
- `closeOnBackdrop` and `closeOnEscape` are now explicit props
- Body scroll prevention is automatic

### Form Validation
- Manual validation replaced with `validateField` utility
- Form state management centralized in `useForm` hook
- Consistent error display with `FormMessage`

## Benefits

1. **Consistency**: All components follow the same patterns and styling
2. **Accessibility**: Built-in ARIA attributes and keyboard navigation
3. **Performance**: Optimized re-renders and event handling
4. **Developer Experience**: Better TypeScript support and IntelliSense
5. **Maintainability**: Single source of truth for component behavior
6. **Testing**: Easier to test with consistent APIs

## Migration Checklist

- [ ] Update button imports to use unified system
- [ ] Replace custom modals with unified Modal component
- [ ] Migrate forms to use FormField pattern
- [ ] Update loading states to use unified Loading components
- [ ] Test all interactive components for accessibility
- [ ] Update tests to use new component APIs
- [ ] Remove old component files after migration

## Next Steps

After completing this migration:
1. Run the validation script: `pnpm refactor:validate`
2. Update component documentation
3. Train team on new component patterns
4. Consider adding Storybook stories for new components
