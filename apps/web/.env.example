# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/buddychip
DIRECT_URL=postgresql://user:password@localhost:5432/buddychip

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_...
CLERK_WEBHOOK_SIGNING_SECRET=whsec_...
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...

# Clerk Billing (Optional - for custom billing integration)
CLERK_JWT_KEY=your_clerk_jwt_key

# AI & OpenRouter Configuration
OPENROUTER_API_KEY=your_openrouter_api_key
OR_SITE_URL=your_site_url
OR_APP_NAME=BuddyChip

# AI Tools
OPENAI_API_KEY=your_openai_api_key
XAI_API_KEY=your_xai_api_key
PERPLEXITY_API_KEY=your_perplexity_api_key
EXA_API_KEY=your_exa_api_key

# Crypto Intelligence
COOKIE_API_KEY=your_cookie_fun_api_key

# File Storage
UPLOADTHING_TOKEN=your_uploadthing_token

# Twitter API (TwitterAPI.io)
TWITTER_API_KEY=your_twitterapi_io_api_key

# Memory Store (Mem0)
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE=your_supabase_service_role_key
MEM0_API_KEY=your_mem0_api_key

# Rate Limiting
KV_URL=your_upstash_kv_url
KV_TOKEN=your_upstash_kv_token

# Sentry Error Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_AUTH_TOKEN=your_sentry_auth_token

# CORS Configuration
CORS_ORIGIN=http://localhost:3001
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://yourdomain.com

# Security Configuration
SYNC_API_KEY=your_secure_sync_api_key_here
RATE_LIMIT_ENABLED=true
SECURITY_HEADERS_ENABLED=true
AUDIT_LOGGING_ENABLED=true

# Logging Configuration
VERBOSE_LOGGING=false
ENABLE_PRISMA_QUERY_LOGS=false
ENABLE_CONTEXT_LOGS=false
ENABLE_TRPC_REQUEST_LOGS=false

# Refactoring Feature Flags
USE_NEW_UTILITY_LIBRARY=true
USE_UNIFIED_COMPONENTS=false
USE_NEW_BUTTON_SYSTEM=false
USE_STANDARDIZED_MODALS=false
USE_UNIFIED_FORMS=false
USE_NEW_TRPC_MIDDLEWARE=false
USE_CONSOLIDATED_ROUTERS=false
USE_COMMON_SCHEMAS=false
USE_OPTIMIZED_QUERIES=false
USE_BATCH_OPERATIONS=false
USE_QUERY_CACHE=false
USE_MODULAR_MENTIONS=false
USE_MODULAR_TELEGRAM=false
USE_MODULAR_COOKIE_CLIENT=false
USE_NEW_BENJI_AGENT=false
USE_BENJI_SERVICES=false

# Development & Monitoring Flags
ENABLE_REFACTOR_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_QUALITY_CHECKS=true
ENABLE_MIGRATION_TRACKING=true
