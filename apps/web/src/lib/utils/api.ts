/**
 * API Utilities for BuddyChip
 * 
 * Comprehensive utilities for API operations including retry logic,
 * response formatting, and typed client creation.
 */

import { errorUtils } from "./errors";

/**
 * Standard API response types
 */
export type SuccessResponse<T> = {
  success: true;
  data: T;
  message?: string;
  metadata?: Record<string, any>;
};

export type ErrorResponse = {
  success: false;
  error: string;
  code?: string;
  details?: Record<string, any>;
};

export type APIResponse<T> = SuccessResponse<T> | ErrorResponse;

/**
 * Paginated response type
 */
export type PaginatedResponse<T> = {
  success: true;
  data: T[];
  pagination: {
    nextCursor?: string;
    hasNextPage: boolean;
    totalCount?: number;
    limit: number;
  };
};

/**
 * Retry configuration options
 */
export interface RetryOptions {
  maxRetries?: number;
  initialDelay?: number;
  maxDelay?: number;
  backoffMultiplier?: number;
  retryOn?: (error: Error) => boolean;
  onRetry?: (error: Error, attempt: number) => void;
}

/**
 * API client configuration
 */
export interface APIClientConfig {
  baseURL: string;
  defaultHeaders?: HeadersInit;
  timeout?: number;
  retryOptions?: RetryOptions;
}

/**
 * Core API utilities
 */
export const apiUtils = {
  /**
   * Create success response with consistent structure
   */
  createSuccessResponse: <T>(
    data: T,
    message?: string,
    metadata?: Record<string, any>
  ): SuccessResponse<T> => ({
    success: true,
    data,
    message,
    metadata,
  }),
  
  /**
   * Create error response with consistent structure
   */
  createErrorResponse: (
    error: string,
    code?: string,
    details?: Record<string, any>
  ): ErrorResponse => ({
    success: false,
    error,
    code,
    details,
  }),
  
  /**
   * Create paginated response
   */
  createPaginatedResponse: <T>(
    data: T[],
    pagination: {
      nextCursor?: string;
      hasNextPage: boolean;
      totalCount?: number;
      limit: number;
    }
  ): PaginatedResponse<T> => ({
    success: true,
    data,
    pagination,
  }),
  
  /**
   * Retry operation with exponential backoff
   */
  withRetry: async <T>(
    operation: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> => {
    const {
      maxRetries = 3,
      initialDelay = 1000,
      maxDelay = 10000,
      backoffMultiplier = 2,
      retryOn = errorUtils.isRetryableError,
      onRetry,
    } = options;
    
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on last attempt or if error is not retryable
        if (attempt === maxRetries || !retryOn(lastError)) {
          throw lastError;
        }
        
        // Calculate delay with exponential backoff
        const delay = Math.min(
          initialDelay * Math.pow(backoffMultiplier, attempt),
          maxDelay
        );
        
        // Call retry callback if provided
        onRetry?.(lastError, attempt + 1);
        
        console.warn(`🔄 Retrying operation (attempt ${attempt + 1}/${maxRetries}) after ${delay}ms:`, {
          error: lastError.message,
          attempt: attempt + 1,
          delay,
        });
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  },
  
  /**
   * Create typed API client with retry and error handling
   */
  createAPIClient: (config: APIClientConfig) => {
    const { baseURL, defaultHeaders = {}, timeout = 30000, retryOptions = {} } = config;
    
    return {
      async request<T>(
        endpoint: string,
        options: RequestInit & {
          retryOptions?: RetryOptions;
        } = {}
      ): Promise<T> {
        const { retryOptions: requestRetryOptions, ...fetchOptions } = options;
        const finalRetryOptions = { ...retryOptions, ...requestRetryOptions };
        
        return apiUtils.withRetry(async () => {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), timeout);
          
          try {
            const response = await fetch(`${baseURL}${endpoint}`, {
              ...fetchOptions,
              headers: {
                "Content-Type": "application/json",
                ...defaultHeaders,
                ...fetchOptions.headers,
              },
              signal: controller.signal,
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
              const errorText = await response.text();
              let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
              
              try {
                const errorData = JSON.parse(errorText);
                errorMessage = errorData.message || errorData.error || errorMessage;
              } catch {
                // Use default error message if response is not JSON
              }
              
              throw new Error(errorMessage);
            }
            
            const contentType = response.headers.get("content-type");
            if (contentType?.includes("application/json")) {
              return response.json();
            }
            
            return response.text() as T;
          } catch (error) {
            clearTimeout(timeoutId);
            
            if (error instanceof Error && error.name === "AbortError") {
              throw new Error(`Request timeout after ${timeout}ms`);
            }
            
            throw error;
          }
        }, finalRetryOptions);
      },
      
      async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
        return this.request<T>(endpoint, { ...options, method: "GET" });
      },
      
      async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
        return this.request<T>(endpoint, {
          ...options,
          method: "POST",
          body: data ? JSON.stringify(data) : undefined,
        });
      },
      
      async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
        return this.request<T>(endpoint, {
          ...options,
          method: "PUT",
          body: data ? JSON.stringify(data) : undefined,
        });
      },
      
      async patch<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
        return this.request<T>(endpoint, {
          ...options,
          method: "PATCH",
          body: data ? JSON.stringify(data) : undefined,
        });
      },
      
      async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
        return this.request<T>(endpoint, { ...options, method: "DELETE" });
      },
    };
  },
  
  /**
   * Parse pagination cursor safely
   */
  parseCursor: (cursor?: string): Record<string, any> | null => {
    if (!cursor) return null;
    
    try {
      return JSON.parse(Buffer.from(cursor, "base64").toString());
    } catch (error) {
      console.warn("Failed to parse pagination cursor:", error);
      return null;
    }
  },
  
  /**
   * Create pagination cursor
   */
  createCursor: (data: Record<string, any>): string => {
    return Buffer.from(JSON.stringify(data)).toString("base64");
  },
  
  /**
   * Validate API response structure
   */
  isValidResponse: <T>(response: any): response is APIResponse<T> => {
    return (
      typeof response === "object" &&
      response !== null &&
      typeof response.success === "boolean"
    );
  },
  
  /**
   * Extract data from API response safely
   */
  extractData: <T>(response: APIResponse<T>): T => {
    if (!response.success) {
      throw new Error(response.error);
    }
    return response.data;
  },
  
  /**
   * Handle API errors consistently
   */
  handleAPIError: (error: unknown, context: string): never => {
    if (error instanceof Error) {
      errorUtils.logError(error, {
        operation: `api_${context}`,
        metadata: { context },
      });
      
      throw errorUtils.createTRPCError(
        "INTERNAL_SERVER_ERROR",
        `API operation failed: ${context}`,
        error
      );
    }
    
    throw errorUtils.createTRPCError(
      "INTERNAL_SERVER_ERROR",
      `Unknown API error in ${context}`
    );
  },
  
  /**
   * Rate limit utilities
   */
  rateLimit: {
    /**
     * Simple in-memory rate limiter
     */
    createLimiter: (maxRequests: number, windowMs: number) => {
      const requests = new Map<string, number[]>();
      
      return {
        check: (key: string): boolean => {
          const now = Date.now();
          const windowStart = now - windowMs;
          
          // Get existing requests for this key
          const keyRequests = requests.get(key) || [];
          
          // Filter out old requests
          const recentRequests = keyRequests.filter(time => time > windowStart);
          
          // Check if limit exceeded
          if (recentRequests.length >= maxRequests) {
            return false;
          }
          
          // Add current request
          recentRequests.push(now);
          requests.set(key, recentRequests);
          
          return true;
        },
        
        reset: (key: string): void => {
          requests.delete(key);
        },
        
        getRemaining: (key: string): number => {
          const now = Date.now();
          const windowStart = now - windowMs;
          const keyRequests = requests.get(key) || [];
          const recentRequests = keyRequests.filter(time => time > windowStart);
          
          return Math.max(0, maxRequests - recentRequests.length);
        },
      };
    },
  },
} as const;

/**
 * HTTP status code utilities
 */
export const httpUtils = {
  isSuccess: (status: number): boolean => status >= 200 && status < 300,
  isRedirect: (status: number): boolean => status >= 300 && status < 400,
  isClientError: (status: number): boolean => status >= 400 && status < 500,
  isServerError: (status: number): boolean => status >= 500 && status < 600,
  
  getStatusMessage: (status: number): string => {
    const messages: Record<number, string> = {
      200: "OK",
      201: "Created",
      204: "No Content",
      400: "Bad Request",
      401: "Unauthorized",
      403: "Forbidden",
      404: "Not Found",
      409: "Conflict",
      429: "Too Many Requests",
      500: "Internal Server Error",
      502: "Bad Gateway",
      503: "Service Unavailable",
    };
    
    return messages[status] || "Unknown Status";
  },
} as const;

console.log("🌐 API utilities loaded successfully");
