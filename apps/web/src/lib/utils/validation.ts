/**
 * Validation Utilities for BuddyChip
 * 
 * Comprehensive validation utilities for input sanitization,
 * schema validation, and security checks.
 */

import { z } from "zod";
import DOMPurify from "isomorphic-dompurify";

/**
 * Common validation schemas
 */
export const commonSchemas = {
  // Pagination schema
  pagination: z.object({
    limit: z.number().min(1).max(100).default(50),
    cursor: z.string().optional(),
  }),
  
  // Date range schema
  dateRange: z.object({
    startDate: z.date().optional(),
    endDate: z.date().optional(),
  }).refine(data => {
    if (data.startDate && data.endDate) {
      return data.startDate <= data.endDate;
    }
    return true;
  }, "Start date must be before end date"),
  
  // ID schemas
  id: z.object({
    id: z.string().cuid("Invalid ID format"),
  }),
  
  ids: z.object({
    ids: z.array(z.string().cuid()).min(1).max(100),
  }),
  
  // Twitter handle schema
  twitterHandle: z
    .string()
    .regex(/^@?[a-zA-Z0-9_]{1,15}$/, "Invalid Twitter handle")
    .transform(val => val.replace("@", "")),
  
  // URL schema
  url: z
    .string()
    .url("Invalid URL")
    .refine(url => {
      try {
        const parsed = new URL(url);
        return ["http:", "https:"].includes(parsed.protocol);
      } catch {
        return false;
      }
    }, "URL must use HTTP or HTTPS protocol"),
  
  // Email schema
  email: z.string().email("Invalid email address"),
  
  // Phone number schema (basic)
  phone: z.string().regex(/^\+?[\d\s\-\(\)]{10,}$/, "Invalid phone number"),
  
  // Password schema
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/\d/, "Password must contain at least one number"),
  
  // Text content schema
  textContent: z
    .string()
    .min(1, "Content cannot be empty")
    .max(10000, "Content too long")
    .transform(val => val.trim()),
  
  // Social media content
  tweetContent: z
    .string()
    .min(1, "Tweet cannot be empty")
    .max(280, "Tweet exceeds character limit")
    .transform(val => val.trim()),
} as const;

/**
 * Response schemas for consistent API responses
 */
export const responseSchemas = {
  success: <T extends z.ZodSchema>(dataSchema: T) =>
    z.object({
      success: z.literal(true),
      data: dataSchema,
      message: z.string().optional(),
      metadata: z.record(z.any()).optional(),
    }),
  
  error: z.object({
    success: z.literal(false),
    error: z.string(),
    code: z.string().optional(),
    details: z.record(z.any()).optional(),
  }),
  
  paginated: <T extends z.ZodSchema>(itemSchema: T) =>
    z.object({
      success: z.literal(true),
      data: z.array(itemSchema),
      pagination: z.object({
        nextCursor: z.string().optional(),
        hasNextPage: z.boolean(),
        totalCount: z.number().optional(),
        limit: z.number(),
      }),
    }),
} as const;

/**
 * Core validation utilities
 */
export const validationUtils = {
  /**
   * Sanitize HTML content safely
   */
  sanitizeHTML: (html: string, options?: {
    allowedTags?: string[];
    allowedAttributes?: string[];
  }): string => {
    const { allowedTags = ["b", "i", "em", "strong", "a", "br", "p"], allowedAttributes = ["href", "target", "rel"] } = options || {};
    
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: allowedTags,
      ALLOWED_ATTR: allowedAttributes,
      ALLOW_DATA_ATTR: false,
    });
  },
  
  /**
   * Sanitize plain text input
   */
  sanitizeText: (text: string): string => {
    return text
      .replace(/[<>]/g, "") // Remove potential HTML
      .replace(/\\/g, "\\\\") // Escape backslashes
      .replace(/"/g, '\\"') // Escape quotes
      .trim();
  },
  
  /**
   * Validate and sanitize Twitter handle
   */
  validateTwitterHandle: (handle: string): string | null => {
    try {
      return commonSchemas.twitterHandle.parse(handle);
    } catch {
      return null;
    }
  },
  
  /**
   * Validate URL with additional security checks
   */
  validateURL: (url: string): boolean => {
    try {
      const parsed = new URL(url);
      
      // Check protocol
      if (!["http:", "https:"].includes(parsed.protocol)) {
        return false;
      }
      
      // Check for suspicious patterns
      const suspiciousPatterns = [
        /javascript:/i,
        /data:/i,
        /vbscript:/i,
        /file:/i,
        /ftp:/i,
      ];
      
      return !suspiciousPatterns.some(pattern => pattern.test(url));
    } catch {
      return false;
    }
  },
  
  /**
   * Validate email with additional checks
   */
  validateEmail: (email: string): boolean => {
    try {
      commonSchemas.email.parse(email);
      
      // Additional checks for suspicious patterns
      const suspiciousPatterns = [
        /\+.*\+/, // Multiple plus signs
        /\.{2,}/, // Multiple consecutive dots
        /@.*@/, // Multiple @ symbols
      ];
      
      return !suspiciousPatterns.some(pattern => pattern.test(email));
    } catch {
      return false;
    }
  },
  
  /**
   * Create paginated request schema with custom filters
   */
  createPaginatedSchema: <T extends z.ZodSchema>(filterSchema: T) => {
    return commonSchemas.pagination.merge(filterSchema);
  },
  
  /**
   * Validate file upload
   */
  validateFile: (file: File, options: {
    maxSize?: number; // in bytes
    allowedTypes?: string[];
    allowedExtensions?: string[];
  }): { valid: boolean; error?: string } => {
    const { maxSize = 5 * 1024 * 1024, allowedTypes = [], allowedExtensions = [] } = options;
    
    // Check file size
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size exceeds ${Math.round(maxSize / 1024 / 1024)}MB limit`,
      };
    }
    
    // Check MIME type
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `File type ${file.type} is not allowed`,
      };
    }
    
    // Check file extension
    if (allowedExtensions.length > 0) {
      const extension = file.name.split(".").pop()?.toLowerCase();
      if (!extension || !allowedExtensions.includes(extension)) {
        return {
          valid: false,
          error: `File extension .${extension} is not allowed`,
        };
      }
    }
    
    return { valid: true };
  },
  
  /**
   * Validate JSON string safely
   */
  validateJSON: (jsonString: string): { valid: boolean; data?: any; error?: string } => {
    try {
      const data = JSON.parse(jsonString);
      return { valid: true, data };
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : "Invalid JSON",
      };
    }
  },
  
  /**
   * Check for SQL injection patterns
   */
  hasSQLInjection: (input: string): boolean => {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /(--|\/\*|\*\/)/,
      /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)/i,
      /(<script|<\/script>)/i,
    ];
    
    return sqlPatterns.some(pattern => pattern.test(input));
  },
  
  /**
   * Check for XSS patterns
   */
  hasXSS: (input: string): boolean => {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe/i,
      /<object/i,
      /<embed/i,
      /<link/i,
      /<meta/i,
    ];
    
    return xssPatterns.some(pattern => pattern.test(input));
  },
  
  /**
   * Comprehensive input sanitization
   */
  sanitizeInput: (input: any): any => {
    if (typeof input === "string") {
      // Check for malicious patterns
      if (validationUtils.hasSQLInjection(input) || validationUtils.hasXSS(input)) {
        console.warn("🚨 Potentially malicious input detected:", input.substring(0, 100));
        return validationUtils.sanitizeText(input);
      }
      
      return validationUtils.sanitizeText(input);
    }
    
    if (Array.isArray(input)) {
      return input.map(item => validationUtils.sanitizeInput(item));
    }
    
    if (typeof input === "object" && input !== null) {
      const sanitized: Record<string, any> = {};
      for (const [key, value] of Object.entries(input)) {
        sanitized[validationUtils.sanitizeText(key)] = validationUtils.sanitizeInput(value);
      }
      return sanitized;
    }
    
    return input;
  },
  
  /**
   * Rate limiting validation
   */
  validateRateLimit: (
    requests: number,
    limit: number,
    windowMs: number,
    timestamps: number[]
  ): { allowed: boolean; remaining: number; resetTime: number } => {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Filter recent requests
    const recentRequests = timestamps.filter(time => time > windowStart);
    const remaining = Math.max(0, limit - recentRequests.length);
    const resetTime = recentRequests.length > 0 ? recentRequests[0] + windowMs : now + windowMs;
    
    return {
      allowed: recentRequests.length < limit,
      remaining,
      resetTime,
    };
  },
  
  /**
   * Password strength validation
   */
  validatePasswordStrength: (password: string): {
    score: number; // 0-4
    feedback: string[];
    isStrong: boolean;
  } => {
    const feedback: string[] = [];
    let score = 0;
    
    // Length check
    if (password.length >= 8) score++;
    else feedback.push("Use at least 8 characters");
    
    // Uppercase check
    if (/[A-Z]/.test(password)) score++;
    else feedback.push("Include uppercase letters");
    
    // Lowercase check
    if (/[a-z]/.test(password)) score++;
    else feedback.push("Include lowercase letters");
    
    // Number check
    if (/\d/.test(password)) score++;
    else feedback.push("Include numbers");
    
    // Special character check
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;
    else feedback.push("Include special characters");
    
    // Common patterns check
    const commonPatterns = [
      /123456/,
      /password/i,
      /qwerty/i,
      /abc123/i,
    ];
    
    if (commonPatterns.some(pattern => pattern.test(password))) {
      score = Math.max(0, score - 1);
      feedback.push("Avoid common patterns");
    }
    
    return {
      score: Math.min(4, score),
      feedback,
      isStrong: score >= 3,
    };
  },
} as const;

/**
 * Schema composition utilities
 */
export const schemaUtils = {
  /**
   * Create optional version of schema
   */
  optional: <T extends z.ZodSchema>(schema: T) => schema.optional(),
  
  /**
   * Create nullable version of schema
   */
  nullable: <T extends z.ZodSchema>(schema: T) => schema.nullable(),
  
  /**
   * Create array version of schema with validation
   */
  array: <T extends z.ZodSchema>(schema: T, options?: { min?: number; max?: number }) => {
    let arraySchema = z.array(schema);
    
    if (options?.min !== undefined) {
      arraySchema = arraySchema.min(options.min);
    }
    
    if (options?.max !== undefined) {
      arraySchema = arraySchema.max(options.max);
    }
    
    return arraySchema;
  },
  
  /**
   * Create union schema with error handling
   */
  union: <T extends readonly [z.ZodSchema, z.ZodSchema, ...z.ZodSchema[]]>(schemas: T) => {
    return z.union(schemas);
  },
} as const;

console.log("✅ Validation utilities loaded successfully");
