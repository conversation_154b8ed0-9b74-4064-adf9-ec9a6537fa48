/**
 * Performance Utilities for BuddyChip
 * 
 * Comprehensive performance utilities including debouncing, throttling,
 * caching, memoization, and performance monitoring.
 */

/**
 * Cache entry interface
 */
interface CacheEntry<T> {
  value: T;
  expires: number;
  hits: number;
  created: number;
}

/**
 * Performance monitoring interface
 */
interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

/**
 * Core performance utilities
 */
export const performanceUtils = {
  /**
   * Debounce function calls to prevent excessive execution
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  },
  
  /**
   * Throttle function calls to limit execution frequency
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle = false;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  },
  
  /**
   * Advanced debounce with immediate execution option
   */
  debounceAdvanced: <T extends (...args: any[]) => any>(
    func: T,
    delay: number,
    options: {
      immediate?: boolean;
      maxWait?: number;
    } = {}
  ): {
    (...args: Parameters<T>): void;
    cancel: () => void;
    flush: () => void;
  } => {
    let timeoutId: NodeJS.Timeout | null = null;
    let maxTimeoutId: NodeJS.Timeout | null = null;
    let lastCallTime = 0;
    let lastArgs: Parameters<T> | null = null;
    
    const { immediate = false, maxWait } = options;
    
    const execute = () => {
      if (lastArgs) {
        func(...lastArgs);
        lastArgs = null;
      }
    };
    
    const debounced = (...args: Parameters<T>) => {
      lastArgs = args;
      lastCallTime = Date.now();
      
      if (timeoutId) clearTimeout(timeoutId);
      if (maxTimeoutId) clearTimeout(maxTimeoutId);
      
      if (immediate && !timeoutId) {
        execute();
      }
      
      timeoutId = setTimeout(() => {
        if (!immediate) execute();
        timeoutId = null;
        maxTimeoutId = null;
      }, delay);
      
      if (maxWait && !maxTimeoutId) {
        maxTimeoutId = setTimeout(() => {
          execute();
          if (timeoutId) clearTimeout(timeoutId);
          timeoutId = null;
          maxTimeoutId = null;
        }, maxWait);
      }
    };
    
    debounced.cancel = () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (maxTimeoutId) clearTimeout(maxTimeoutId);
      timeoutId = null;
      maxTimeoutId = null;
      lastArgs = null;
    };
    
    debounced.flush = () => {
      if (timeoutId) {
        execute();
        debounced.cancel();
      }
    };
    
    return debounced;
  },
  
  /**
   * Create high-performance in-memory cache with TTL and LRU eviction
   */
  createCache: <T>(options: {
    ttl?: number;
    maxSize?: number;
    onEvict?: (key: string, value: T) => void;
  } = {}) => {
    const { ttl = 5 * 60 * 1000, maxSize = 1000, onEvict } = options;
    const cache = new Map<string, CacheEntry<T>>();
    const accessOrder = new Map<string, number>();
    let accessCounter = 0;
    
    const evictExpired = () => {
      const now = Date.now();
      for (const [key, entry] of cache.entries()) {
        if (entry.expires < now) {
          onEvict?.(key, entry.value);
          cache.delete(key);
          accessOrder.delete(key);
        }
      }
    };
    
    const evictLRU = () => {
      if (cache.size <= maxSize) return;
      
      // Find least recently used item
      let lruKey = "";
      let lruAccess = Infinity;
      
      for (const [key, access] of accessOrder.entries()) {
        if (access < lruAccess) {
          lruAccess = access;
          lruKey = key;
        }
      }
      
      if (lruKey) {
        const entry = cache.get(lruKey);
        if (entry) {
          onEvict?.(lruKey, entry.value);
        }
        cache.delete(lruKey);
        accessOrder.delete(lruKey);
      }
    };
    
    return {
      get: (key: string): T | null => {
        evictExpired();
        
        const entry = cache.get(key);
        if (!entry || entry.expires < Date.now()) {
          cache.delete(key);
          accessOrder.delete(key);
          return null;
        }
        
        // Update access order
        entry.hits++;
        accessOrder.set(key, ++accessCounter);
        
        return entry.value;
      },
      
      set: (key: string, value: T, customTtl?: number): void => {
        evictExpired();
        evictLRU();
        
        const now = Date.now();
        const entry: CacheEntry<T> = {
          value,
          expires: now + (customTtl || ttl),
          hits: 0,
          created: now,
        };
        
        cache.set(key, entry);
        accessOrder.set(key, ++accessCounter);
      },
      
      has: (key: string): boolean => {
        evictExpired();
        return cache.has(key);
      },
      
      delete: (key: string): boolean => {
        const entry = cache.get(key);
        if (entry) {
          onEvict?.(key, entry.value);
        }
        accessOrder.delete(key);
        return cache.delete(key);
      },
      
      clear: (): void => {
        for (const [key, entry] of cache.entries()) {
          onEvict?.(key, entry.value);
        }
        cache.clear();
        accessOrder.clear();
      },
      
      size: (): number => {
        evictExpired();
        return cache.size;
      },
      
      stats: () => {
        evictExpired();
        const entries = Array.from(cache.values());
        const totalHits = entries.reduce((sum, entry) => sum + entry.hits, 0);
        const avgAge = entries.length > 0 
          ? entries.reduce((sum, entry) => sum + (Date.now() - entry.created), 0) / entries.length
          : 0;
        
        return {
          size: cache.size,
          totalHits,
          avgAge: Math.round(avgAge),
          hitRate: totalHits / Math.max(1, cache.size),
        };
      },
    };
  },
  
  /**
   * Memoize function results with cache
   */
  memoize: <T extends (...args: any[]) => any>(
    func: T,
    options: {
      keyGenerator?: (...args: Parameters<T>) => string;
      ttl?: number;
      maxSize?: number;
    } = {}
  ): T & { cache: ReturnType<typeof performanceUtils.createCache>; clearCache: () => void } => {
    const {
      keyGenerator = (...args) => JSON.stringify(args),
      ttl = 5 * 60 * 1000,
      maxSize = 100,
    } = options;
    
    const cache = performanceUtils.createCache<ReturnType<T>>({ ttl, maxSize });
    
    const memoized = ((...args: Parameters<T>) => {
      const key = keyGenerator(...args);
      const cached = cache.get(key);
      
      if (cached !== null) {
        return cached;
      }
      
      const result = func(...args);
      cache.set(key, result);
      return result;
    }) as T & { cache: typeof cache; clearCache: () => void };
    
    memoized.cache = cache;
    memoized.clearCache = () => cache.clear();
    
    return memoized;
  },
  
  /**
   * Measure function execution time
   */
  measureTime: async <T>(
    operation: () => Promise<T> | T,
    name?: string
  ): Promise<{ result: T; duration: number; metric: PerformanceMetric }> => {
    const start = performance.now();
    const result = await operation();
    const duration = performance.now() - start;
    
    const metric: PerformanceMetric = {
      name: name || "anonymous_operation",
      duration,
      timestamp: Date.now(),
    };
    
    if (duration > 1000) {
      console.warn(`⚠️ Slow operation detected: ${metric.name} took ${duration.toFixed(2)}ms`);
    }
    
    return { result, duration, metric };
  },
  
  /**
   * Batch operations for better performance
   */
  batch: <T, R>(
    items: T[],
    processor: (batch: T[]) => Promise<R[]>,
    batchSize: number = 10,
    delay: number = 0
  ): Promise<R[]> => {
    return new Promise(async (resolve, reject) => {
      try {
        const results: R[] = [];
        
        for (let i = 0; i < items.length; i += batchSize) {
          const batch = items.slice(i, i + batchSize);
          const batchResults = await processor(batch);
          results.push(...batchResults);
          
          // Add delay between batches if specified
          if (delay > 0 && i + batchSize < items.length) {
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
        
        resolve(results);
      } catch (error) {
        reject(error);
      }
    });
  },
  
  /**
   * Queue operations to prevent overwhelming the system
   */
  createQueue: <T>(options: {
    concurrency?: number;
    delay?: number;
    onError?: (error: Error, item: T) => void;
  } = {}) => {
    const { concurrency = 1, delay = 0, onError } = options;
    const queue: Array<{ item: T; resolve: (value: any) => void; reject: (error: any) => void }> = [];
    let running = 0;
    
    const processNext = async () => {
      if (running >= concurrency || queue.length === 0) return;
      
      running++;
      const { item, resolve, reject } = queue.shift()!;
      
      try {
        const result = await item;
        resolve(result);
      } catch (error) {
        onError?.(error as Error, item);
        reject(error);
      } finally {
        running--;
        
        if (delay > 0) {
          setTimeout(processNext, delay);
        } else {
          processNext();
        }
      }
    };
    
    return {
      add: <R>(item: T): Promise<R> => {
        return new Promise((resolve, reject) => {
          queue.push({ item, resolve, reject });
          processNext();
        });
      },
      
      size: () => queue.length,
      running: () => running,
      
      clear: () => {
        queue.length = 0;
      },
    };
  },
  
  /**
   * Lazy loading utility
   */
  lazy: <T>(factory: () => T): (() => T) => {
    let instance: T;
    let initialized = false;
    
    return () => {
      if (!initialized) {
        instance = factory();
        initialized = true;
      }
      return instance;
    };
  },
  
  /**
   * Performance monitoring utilities
   */
  monitor: {
    metrics: [] as PerformanceMetric[],
    
    record: (metric: PerformanceMetric) => {
      performanceUtils.monitor.metrics.push(metric);
      
      // Keep only last 1000 metrics
      if (performanceUtils.monitor.metrics.length > 1000) {
        performanceUtils.monitor.metrics = performanceUtils.monitor.metrics.slice(-1000);
      }
    },
    
    getStats: (name?: string) => {
      const relevantMetrics = name 
        ? performanceUtils.monitor.metrics.filter(m => m.name === name)
        : performanceUtils.monitor.metrics;
      
      if (relevantMetrics.length === 0) {
        return null;
      }
      
      const durations = relevantMetrics.map(m => m.duration);
      const avg = durations.reduce((sum, d) => sum + d, 0) / durations.length;
      const min = Math.min(...durations);
      const max = Math.max(...durations);
      
      // Calculate percentiles
      const sorted = durations.sort((a, b) => a - b);
      const p50 = sorted[Math.floor(sorted.length * 0.5)];
      const p95 = sorted[Math.floor(sorted.length * 0.95)];
      const p99 = sorted[Math.floor(sorted.length * 0.99)];
      
      return {
        count: relevantMetrics.length,
        avg: Math.round(avg * 100) / 100,
        min: Math.round(min * 100) / 100,
        max: Math.round(max * 100) / 100,
        p50: Math.round(p50 * 100) / 100,
        p95: Math.round(p95 * 100) / 100,
        p99: Math.round(p99 * 100) / 100,
      };
    },
    
    clear: () => {
      performanceUtils.monitor.metrics = [];
    },
  },
} as const;

/**
 * React-specific performance utilities
 */
export const reactPerformanceUtils = {
  /**
   * Create stable callback reference
   */
  useStableCallback: <T extends (...args: any[]) => any>(callback: T): T => {
    // This would be implemented as a React hook in actual usage
    return callback;
  },
  
  /**
   * Optimize re-renders by memoizing props
   */
  createMemoizedProps: <T extends Record<string, any>>(props: T): T => {
    // This would use React.useMemo in actual implementation
    return props;
  },
} as const;

console.log("⚡ Performance utilities loaded successfully");
