/**
 * Unified tRPC Middleware System
 * 
 * Consolidates common middleware patterns used across all routers:
 * - Rate limiting with subscription plan integration
 * - Usage tracking and logging
 * - Error handling and monitoring
 * - Security validation
 * - Performance monitoring
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import type { FeatureType } from "../../prisma/generated/index.js";
import { checkRateLimit, recordUsage } from "./db-utils";
import { logSecurityEvent } from "./security-utils";
import { t } from "./trpc";
import { canUserUseFeature, logUsage } from "./user-service";
import { performanceMonitor } from "./performance-monitor";

/**
 * Rate limiting middleware with subscription plan integration
 */
export const rateLimitMiddleware = (feature: FeatureType, requestedAmount: number = 1) =>
  t.middleware(async ({ ctx, next, path }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required for rate limiting",
      });
    }

    console.log(`🚦 Rate limit check: ${feature} for user ${ctx.userId} (${requestedAmount} units)`);

    try {
      const rateLimitResult = await checkRateLimit(ctx.userId, feature, requestedAmount);

      if (!rateLimitResult.allowed) {
        // Log rate limit violation for security monitoring
        logSecurityEvent({
          type: "RATE_LIMIT_EXCEEDED",
          userId: ctx.userId,
          details: {
            feature,
            requestedAmount,
            currentUsage: rateLimitResult.currentUsage,
            limit: rateLimitResult.limit,
            path,
          },
        });

        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Rate limit exceeded for ${feature}. Used ${rateLimitResult.currentUsage}/${rateLimitResult.limit}. Try again later.`,
        });
      }

      console.log(`✅ Rate limit passed: ${rateLimitResult.remaining} remaining`);

      return next({
        ctx: {
          ...ctx,
          rateLimitInfo: rateLimitResult,
        },
      });
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error("Rate limit check failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to check rate limits",
      });
    }
  });

/**
 * Usage tracking middleware - records successful operations
 */
export const usageTrackingMiddleware = (feature: FeatureType, amount: number = 1) =>
  t.middleware(async ({ ctx, next, path }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required for usage tracking",
      });
    }

    // Execute the procedure first
    const result = await next();

    // Only record usage if the operation was successful
    if (result.ok) {
      try {
        await recordUsage(ctx.userId, feature, amount);
        await logUsage(ctx.userId, feature, amount);
        
        console.log(`📊 Usage recorded: ${feature} (${amount} units) for user ${ctx.userId}`);
      } catch (error) {
        // Don't fail the request if usage tracking fails, but log it
        console.error("Failed to record usage:", error);
      }
    }

    return result;
  });

/**
 * Feature access middleware - checks if user can use a feature
 */
export const featureAccessMiddleware = (feature: FeatureType) =>
  t.middleware(async ({ ctx, next }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required for feature access",
      });
    }

    try {
      const canUse = await canUserUseFeature(ctx.userId, feature);

      if (!canUse.allowed) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: canUse.reason || `Access denied for feature: ${feature}`,
        });
      }

      return next({
        ctx: {
          ...ctx,
          featureAccess: canUse,
        },
      });
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error("Feature access check failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to check feature access",
      });
    }
  });

/**
 * Performance monitoring middleware
 */
export const performanceMiddleware = (operationName: string) =>
  t.middleware(async ({ ctx, next, path }) => {
    const startTime = performance.now();
    
    try {
      const result = await next();
      const duration = performance.now() - startTime;
      
      // Record performance metrics
      performanceMonitor.recordMetric({
        name: operationName,
        duration,
        path: path || 'unknown',
        userId: ctx.userId,
        success: result.ok,
        timestamp: Date.now(),
      });

      console.log(`⚡ ${operationName} completed in ${duration.toFixed(2)}ms`);
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      // Record failed operation
      performanceMonitor.recordMetric({
        name: operationName,
        duration,
        path: path || 'unknown',
        userId: ctx.userId,
        success: false,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  });

/**
 * Security validation middleware
 */
export const securityMiddleware = t.middleware(async ({ ctx, next, input, path }) => {
  // Log security-relevant operations
  if (ctx.userId) {
    logSecurityEvent({
      type: "API_ACCESS",
      userId: ctx.userId,
      details: {
        path,
        timestamp: new Date().toISOString(),
        userAgent: ctx.req?.headers.get?.("user-agent"),
        origin: ctx.req?.headers.get?.("origin"),
      },
    });
  }

  return next();
});

/**
 * Combined middleware for common patterns
 */
export const createFeatureMiddleware = (
  feature: FeatureType,
  options: {
    requestedAmount?: number;
    operationName?: string;
    requireFeatureAccess?: boolean;
  } = {}
) => {
  const {
    requestedAmount = 1,
    operationName = feature,
    requireFeatureAccess = true,
  } = options;

  const middlewares = [securityMiddleware];

  if (requireFeatureAccess) {
    middlewares.push(featureAccessMiddleware(feature));
  }

  middlewares.push(
    rateLimitMiddleware(feature, requestedAmount),
    performanceMiddleware(operationName),
    usageTrackingMiddleware(feature, requestedAmount)
  );

  return middlewares;
};

/**
 * Procedure builders with common middleware patterns
 */
export const createFeatureProcedure = (
  feature: FeatureType,
  options?: {
    requestedAmount?: number;
    operationName?: string;
    requireFeatureAccess?: boolean;
  }
) => {
  const middlewares = createFeatureMiddleware(feature, options);
  
  return middlewares.reduce(
    (procedure, middleware) => procedure.use(middleware),
    t.procedure.use(({ ctx, next }) => {
      if (!ctx.userId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You must be logged in to access this resource",
        });
      }
      return next({
        ctx: {
          ...ctx,
          userId: ctx.userId,
        },
      });
    })
  );
};

/**
 * Lightweight procedure for operations that don't need full feature middleware
 */
export const createMonitoredProcedure = (operationName: string) => {
  return t.procedure
    .use(({ ctx, next }) => {
      if (!ctx.userId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You must be logged in to access this resource",
        });
      }
      return next({
        ctx: {
          ...ctx,
          userId: ctx.userId,
        },
      });
    })
    .use(securityMiddleware)
    .use(performanceMiddleware(operationName));
};

/**
 * Error handling utilities for consistent error responses
 */
export const handleTRPCError = (error: unknown, operation: string, context?: Record<string, any>) => {
  console.error(`🚨 tRPC Error in ${operation}:`, { error, context });

  if (error instanceof TRPCError) {
    throw error;
  }

  if (error instanceof Error) {
    // Handle specific error types
    if (error.message.includes("rate limit")) {
      throw new TRPCError({
        code: "TOO_MANY_REQUESTS",
        message: error.message,
      });
    }

    if (error.message.includes("not found")) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: error.message,
      });
    }

    if (error.message.includes("unauthorized") || error.message.includes("access denied")) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: error.message,
      });
    }
  }

  // Generic fallback
  throw new TRPCError({
    code: "INTERNAL_SERVER_ERROR",
    message: `Failed to ${operation}`,
  });
};
