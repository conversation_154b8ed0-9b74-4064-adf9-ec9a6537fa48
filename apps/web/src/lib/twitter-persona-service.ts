/**
 * Twitter Persona Service
 * 
 * Handles fetching Twitter data and generating AI personas from user tweets and replies
 * with integrated Mem0 memory storage for enhanced AI context
 */

import { prisma } from "./db-utils";
import { twitterClient } from "./twitter-client";
import { getBenjiForUser } from "./benji";
import { mem0Service } from "./mem0-service";
import type { TwitterTweet } from "./twitter-client";

// Memory type constants for persona-related memories
export const PERSONA_MEMORY_TYPES = {
  TWEET: "persona_tweet",
  REPLY: "persona_reply", 
  ANALYSIS: "persona_analysis",
  PROFILE: "persona_profile"
} as const;

export interface PersonaGenerationOptions {
  twitterHandle: string;
  tweetsTarget?: number;
  repliesTarget?: number;
  userId: string;
}

export interface PersonaAnalysisResult {
  writingStyle: {
    tone: string;
    formality: string;
    humor: string;
    emotionalRange: string;
    vocabulary: string;
  };
  personalityTraits: {
    primary: string[];
    secondary: string[];
    communication: string[];
  };
  topicsOfInterest: {
    primary: string[];
    secondary: string[];
    expertise: string[];
  };
  engagementPatterns: {
    replyStyle: string;
    questionAsking: string;
    supportiveness: string;
  };
}

export class TwitterPersonaService {
  private userId: string;
  private jobId: string;

  constructor(userId: string, jobId: string) {
    this.userId = userId;
    this.jobId = jobId;
  }

  /**
   * Start the persona generation process
   */
  static async startGeneration(options: PersonaGenerationOptions): Promise<string> {
    console.log(`🎭 Starting persona generation for @${options.twitterHandle}`);

    // Create generation job
    const job = await prisma.personaGenerationJob.create({
      data: {
        userId: options.userId,
        twitterHandle: options.twitterHandle,
        totalTweetsTarget: options.tweetsTarget || 500,
        totalRepliesTarget: options.repliesTarget || 500,
        status: "pending",
      },
    });

    console.log(`📝 Created persona generation job: ${job.id}`);

    // Start the generation process in the background
    const service = new TwitterPersonaService(options.userId, job.id);
    service.processGeneration().catch((error) => {
      console.error(`❌ Persona generation failed for job ${job.id}:`, error);
      service.updateJobStatus("failed", 0, error.message);
    });

    return job.id;
  }

  /**
   * Main processing pipeline
   */
  private async processGeneration(): Promise<void> {
    try {
      console.log(`🔄 Processing persona generation job: ${this.jobId}`);

      // Step 1: Fetch tweets
      await this.updateJobStatus("fetching_tweets", 10);
      const tweets = await this.fetchUserTweets();
      console.log(`📥 Collected ${tweets.length} tweets`);

      // Step 2: Fetch replies
      await this.updateJobStatus("fetching_replies", 30);
      const replies = await this.fetchUserReplies();
      console.log(`📥 Collected ${replies.length} replies`);

      // Step 3: Analyze content
      await this.updateJobStatus("analyzing", 50);
      const analysis = await this.analyzeContent([...tweets, ...replies]);
      console.log(`🧠 Completed content analysis`);

      // Step 4: Generate persona
      await this.updateJobStatus("generating", 80);
      const persona = await this.generatePersona(analysis);
      console.log(`🎭 Generated persona: ${persona.name}`);

      // Step 5: Save to database
      const personalityProfile = await this.savePersona(persona);
      
      // Step 6: Store memories
      await this.updateJobStatus("storing_memories", 90);
      await this.storePersonaMemories(tweets, replies, analysis, personalityProfile);
      console.log(`💾 Stored persona memories`);
      
      // Complete the job
      await this.updateJobStatus("completed", 100, null, personalityProfile.id);
      console.log(`✅ Persona generation completed: ${personalityProfile.id}`);

    } catch (error) {
      console.error(`❌ Error in persona generation:`, error);
      await this.updateJobStatus("failed", 0, error instanceof Error ? error.message : "Unknown error");
      throw error;
    }
  }

  /**
   * Fetch user tweets with pagination
   */
  private async fetchUserTweets(): Promise<TwitterTweet[]> {
    const job = await this.getJob();
    const tweets: TwitterTweet[] = [];
    let cursor: string | undefined;
    let attempts = 0;
    const maxAttempts = 10; // Prevent infinite loops

    console.log(`📥 Fetching up to ${job.totalTweetsTarget} tweets for @${job.twitterHandle}`);

    while (tweets.length < job.totalTweetsTarget && attempts < maxAttempts) {
      try {
        const response = await twitterClient.getUserLastTweets(job.twitterHandle, {
          cursor,
          limit: Math.min(100, job.totalTweetsTarget - tweets.length),
          includeReplies: false, // We'll fetch replies separately
          includeRetweets: false, // Focus on original content
        });

        if (!response.tweets || response.tweets.length === 0) {
          console.log(`📭 No more tweets available for @${job.twitterHandle}`);
          break;
        }

        tweets.push(...response.tweets);
        cursor = response.next_cursor;

        // Update progress
        const progress = Math.min(25, (tweets.length / job.totalTweetsTarget) * 20 + 10);
        await this.updateJobStatus("fetching_tweets", progress, undefined, undefined, tweets.length);

        console.log(`📊 Progress: ${tweets.length}/${job.totalTweetsTarget} tweets collected`);

        if (!response.has_next_page || !cursor) {
          console.log(`📭 Reached end of tweets for @${job.twitterHandle}`);
          break;
        }

        // Rate limiting delay
        await this.delay(1000);
        attempts++;

      } catch (error) {
        console.error(`❌ Error fetching tweets (attempt ${attempts + 1}):`, error);
        if (attempts >= maxAttempts - 1) {
          throw error;
        }
        await this.delay(5000); // Longer delay on error
        attempts++;
      }
    }

    console.log(`✅ Collected ${tweets.length} tweets for analysis`);
    return tweets;
  }

  /**
   * Fetch user replies with pagination
   */
  private async fetchUserReplies(): Promise<TwitterTweet[]> {
    const job = await this.getJob();
    const replies: TwitterTweet[] = [];
    let cursor: string | undefined;
    let attempts = 0;
    const maxAttempts = 10;

    console.log(`📥 Fetching up to ${job.totalRepliesTarget} replies for @${job.twitterHandle}`);

    while (replies.length < job.totalRepliesTarget && attempts < maxAttempts) {
      try {
        const response = await twitterClient.getUserLastTweets(job.twitterHandle, {
          cursor,
          limit: Math.min(100, job.totalRepliesTarget - replies.length),
          includeReplies: true, // Only get replies this time
          includeRetweets: false,
        });

        if (!response.tweets || response.tweets.length === 0) {
          console.log(`📭 No more replies available for @${job.twitterHandle}`);
          break;
        }

        // Filter to only replies (tweets that start with @)
        const actualReplies = response.tweets.filter(tweet => 
          tweet.text.trim().startsWith('@') || tweet.isReply
        );

        replies.push(...actualReplies);
        cursor = response.next_cursor;

        // Update progress
        const progress = Math.min(45, (replies.length / job.totalRepliesTarget) * 15 + 30);
        await this.updateJobStatus("fetching_replies", progress, undefined, undefined, undefined, replies.length);

        console.log(`📊 Progress: ${replies.length}/${job.totalRepliesTarget} replies collected`);

        if (!response.has_next_page || !cursor) {
          console.log(`📭 Reached end of replies for @${job.twitterHandle}`);
          break;
        }

        // Rate limiting delay
        await this.delay(1000);
        attempts++;

      } catch (error) {
        console.error(`❌ Error fetching replies (attempt ${attempts + 1}):`, error);
        if (attempts >= maxAttempts - 1) {
          throw error;
        }
        await this.delay(5000);
        attempts++;
      }
    }

    console.log(`✅ Collected ${replies.length} replies for analysis`);
    return replies;
  }

  /**
   * Analyze collected tweets and replies to extract personality traits
   */
  private async analyzeContent(tweets: TwitterTweet[]): Promise<PersonaAnalysisResult> {
    console.log(`🧠 Analyzing ${tweets.length} tweets for personality traits`);

    if (tweets.length === 0) {
      throw new Error("No tweets available for analysis");
    }

    // Get AI agent for analysis
    const benji = await getBenjiForUser(this.userId);

    // Separate tweets and replies for different analysis
    const regularTweets = tweets.filter(tweet => !tweet.text.trim().startsWith('@') && !tweet.isReply);
    const replies = tweets.filter(tweet => tweet.text.trim().startsWith('@') || tweet.isReply);

    console.log(`📝 Analyzing ${regularTweets.length} tweets and ${replies.length} replies`);

    // Extract text content for analysis
    const tweetTexts = regularTweets.map(tweet => tweet.text);
    const replyTexts = replies.map(tweet => tweet.text);

    // Run all analyses in parallel for better performance
    const [writingStyle, personalityTraits, topicsOfInterest, engagementPatterns] = await Promise.all([
      benji.analyzeWritingStyle(tweetTexts),
      benji.extractPersonalityTraits(tweetTexts),
      benji.identifyTopicsOfInterest(tweetTexts),
      benji.analyzeEngagementPatterns(replyTexts),
    ]);

    console.log(`✅ Completed personality analysis:`, {
      writingStyle,
      personalityTraits,
      topicsOfInterest,
      engagementPatterns
    });

    return {
      writingStyle,
      personalityTraits,
      topicsOfInterest,
      engagementPatterns,
    };
  }

  /**
   * Generate persona from analysis results
   */
  private async generatePersona(analysis: PersonaAnalysisResult): Promise<{
    name: string;
    description: string;
    systemPrompt: string;
    metadata: any;
  }> {
    const job = await this.getJob();
    console.log(`🎭 Generating persona for @${job.twitterHandle}`);

    // Get AI agent for persona generation
    const benji = await getBenjiForUser(this.userId);

    // Generate comprehensive system prompt
    const systemPrompt = await benji.generatePersonaSystemPrompt(job.twitterHandle, analysis);

    // Create persona description
    const primaryTraits = analysis.personalityTraits.primary.join(', ');
    const primaryTopics = analysis.topicsOfInterest.primary.join(', ');

    const description = `AI-generated persona based on @${job.twitterHandle}'s Twitter activity. Key traits: ${primaryTraits}. Main interests: ${primaryTopics}. Writing style: ${analysis.writingStyle.tone} and ${analysis.writingStyle.formality}.`;

    console.log(`✅ Generated persona with ${systemPrompt.length} character system prompt`);

    return {
      name: `@${job.twitterHandle} Persona`,
      description,
      systemPrompt,
      metadata: {
        sourceHandle: job.twitterHandle,
        analysis,
        generatedAt: new Date().toISOString(),
        tweetsAnalyzed: job.tweetsCollected,
        repliesAnalyzed: job.repliesCollected,
        analysisVersion: "1.0"
      }
    };
  }

  /**
   * Save generated persona to database
   */
  private async savePersona(persona: {
    name: string;
    description: string;
    systemPrompt: string;
    metadata: any;
  }) {
    const job = await this.getJob();

    return await prisma.personalityProfile.create({
      data: {
        name: persona.name,
        description: persona.description,
        systemPrompt: persona.systemPrompt,
        isUserGenerated: true,
        sourceTwitterHandle: job.twitterHandle,
        generationMetadata: persona.metadata,
        createdById: this.userId,
        isActive: true,
      },
    });
  }

  /**
   * Update job status and progress
   */
  private async updateJobStatus(
    status: string,
    progress: number,
    errorMessage?: string | null,
    resultPersonalityId?: string | null,
    tweetsCollected?: number,
    repliesCollected?: number
  ): Promise<void> {
    const updateData: any = {
      status,
      progress,
      updatedAt: new Date(),
    };

    if (errorMessage !== undefined) updateData.errorMessage = errorMessage;
    if (resultPersonalityId !== undefined) updateData.resultPersonalityId = resultPersonalityId;
    if (tweetsCollected !== undefined) updateData.tweetsCollected = tweetsCollected;
    if (repliesCollected !== undefined) updateData.repliesCollected = repliesCollected;
    if (status === "completed") updateData.completedAt = new Date();

    await prisma.personaGenerationJob.update({
      where: { id: this.jobId },
      data: updateData,
    });

    console.log(`📊 Job ${this.jobId} status: ${status} (${progress}%)`);
  }

  /**
   * Get current job data
   */
  private async getJob() {
    const job = await prisma.personaGenerationJob.findUnique({
      where: { id: this.jobId },
    });

    if (!job) {
      throw new Error(`Job ${this.jobId} not found`);
    }

    return job;
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get job status (static method for external use)
   */
  static async getJobStatus(jobId: string) {
    return await prisma.personaGenerationJob.findUnique({
      where: { id: jobId },
      include: {
        resultPersonality: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });
  }

  /**
   * Get user's generation jobs
   */
  static async getUserJobs(userId: string) {
    return await prisma.personaGenerationJob.findMany({
      where: { userId },
      include: {
        resultPersonality: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  /**
   * Store persona-related data as memories for enhanced AI context
   */
  private async storePersonaMemories(
    tweets: TwitterTweet[],
    replies: TwitterTweet[],
    analysis: PersonaAnalysisResult,
    personalityProfile: any
  ): Promise<void> {
    try {
      console.log(`💾 Starting memory storage for persona ${personalityProfile.name}`);
      
      let memoriesStored = 0;
      const totalMemoriesToStore = tweets.length + replies.length + 5; // +5 for analysis components and profile
      
      // Store tweet memories in batches for performance
      await this.storeTweetMemories(tweets, PERSONA_MEMORY_TYPES.TWEET, personalityProfile);
      memoriesStored += tweets.length;
      await this.updateMemoryProgress(memoriesStored, totalMemoriesToStore);
      
      // Store reply memories in batches
      await this.storeTweetMemories(replies, PERSONA_MEMORY_TYPES.REPLY, personalityProfile);
      memoriesStored += replies.length;
      await this.updateMemoryProgress(memoriesStored, totalMemoriesToStore);
      
      // Store analysis memories
      await this.storeAnalysisMemories(analysis, personalityProfile);
      memoriesStored += 4;
      await this.updateMemoryProgress(memoriesStored, totalMemoriesToStore);
      
      // Store persona profile memory
      await this.storePersonaProfileMemory(personalityProfile);
      memoriesStored += 1;
      await this.updateMemoryProgress(memoriesStored, totalMemoriesToStore);
      
      console.log(`✅ Successfully stored ${memoriesStored} memories for persona ${personalityProfile.name}`);
      
    } catch (error) {
      console.error(`❌ Error storing persona memories:`, error);
      
      // Update job with memory error but don't fail the entire generation
      await prisma.personaGenerationJob.update({
        where: { id: this.jobId },
        data: {
          memoryErrors: {
            error: error instanceof Error ? error.message : "Unknown memory storage error",
            timestamp: new Date().toISOString()
          }
        }
      });
      
      // Don't throw - let persona generation complete without memories
      console.log(`⚠️ Persona generation will complete without memory storage`);
    }
  }

  /**
   * Store tweet/reply memories in batches
   */
  private async storeTweetMemories(
    tweets: TwitterTweet[],
    memoryType: string,
    personalityProfile: any
  ): Promise<void> {
    const batchSize = 10; // Process in smaller batches to avoid overwhelming the system
    
    for (let i = 0; i < tweets.length; i += batchSize) {
      const batch = tweets.slice(i, i + batchSize);
      
      for (const tweet of batch) {
        try {
          const memoryContent = `@${personalityProfile.sourceTwitterHandle} posted: "${tweet.text}"`;
          const metadata = {
            personaId: personalityProfile.id,
            twitterHandle: personalityProfile.sourceTwitterHandle,
            generationJobId: this.jobId,
            tweetId: tweet.id,
            contentType: memoryType,
            metrics: {
              likes: tweet.likeCount || 0,
              retweets: tweet.retweetCount || 0,
              replies: tweet.replyCount || 0,
            },
            createdAt: tweet.createdAt,
            isReply: tweet.isReply || false,
          };
          
          await mem0Service.addMemories(
            this.userId,
            [{ role: "system", content: memoryContent }],
            {
              userId: this.userId,
              sessionId: this.jobId,
              authorInfo: {
                name: personalityProfile.sourceTwitterHandle,
                handle: personalityProfile.sourceTwitterHandle,
              }
            },
            {
              memoryType,
              metadata
            }
          );
          
        } catch (error) {
          console.error(`❌ Failed to store memory for tweet ${tweet.id}:`, error);
          // Continue with other tweets
        }
      }
      
      // Small delay between batches to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  /**
   * Store analysis results as memories
   */
  private async storeAnalysisMemories(
    analysis: PersonaAnalysisResult,
    personalityProfile: any
  ): Promise<void> {
    const analysisMemories = [
      {
        content: `Writing style analysis for @${personalityProfile.sourceTwitterHandle}: Tone: ${analysis.writingStyle.tone}, Formality: ${analysis.writingStyle.formality}, Humor: ${analysis.writingStyle.humor}`,
        analysisType: "writing_style"
      },
      {
        content: `Personality traits for @${personalityProfile.sourceTwitterHandle}: Primary: ${analysis.personalityTraits.primary.join(", ")}, Secondary: ${analysis.personalityTraits.secondary.join(", ")}`,
        analysisType: "personality_traits"
      },
      {
        content: `Topics of interest for @${personalityProfile.sourceTwitterHandle}: Primary: ${analysis.topicsOfInterest.primary.join(", ")}, Expertise: ${analysis.topicsOfInterest.expertise.join(", ")}`,
        analysisType: "topics_interest"
      },
      {
        content: `Engagement patterns for @${personalityProfile.sourceTwitterHandle}: Reply style: ${analysis.engagementPatterns.replyStyle}, Question asking: ${analysis.engagementPatterns.questionAsking}`,
        analysisType: "engagement_patterns"
      }
    ];
    
    for (const memory of analysisMemories) {
      try {
        const metadata = {
          personaId: personalityProfile.id,
          twitterHandle: personalityProfile.sourceTwitterHandle,
          generationJobId: this.jobId,
          contentType: PERSONA_MEMORY_TYPES.ANALYSIS,
          analysisType: memory.analysisType,
        };
        
        await mem0Service.addMemories(
          this.userId,
          [{ role: "system", content: memory.content }],
          {
            userId: this.userId,
            sessionId: this.jobId,
          },
          {
            memoryType: PERSONA_MEMORY_TYPES.ANALYSIS,
            metadata
          }
        );
        
      } catch (error) {
        console.error(`❌ Failed to store analysis memory:`, error);
      }
    }
  }

  /**
   * Store the persona profile as a memory
   */
  private async storePersonaProfileMemory(personalityProfile: any): Promise<void> {
    try {
      const content = `Generated AI persona "${personalityProfile.name}" based on @${personalityProfile.sourceTwitterHandle}. Description: ${personalityProfile.description}. This persona captures their unique communication style and personality traits.`;
      
      const metadata = {
        personaId: personalityProfile.id,
        twitterHandle: personalityProfile.sourceTwitterHandle,
        generationJobId: this.jobId,
        contentType: PERSONA_MEMORY_TYPES.PROFILE,
        personaName: personalityProfile.name,
      };
      
      await mem0Service.addMemories(
        this.userId,
        [{ role: "system", content }],
        {
          userId: this.userId,
          sessionId: this.jobId,
        },
        {
          memoryType: PERSONA_MEMORY_TYPES.PROFILE,
          metadata
        }
      );
      
    } catch (error) {
      console.error(`❌ Failed to store persona profile memory:`, error);
    }
  }

  /**
   * Update memory storage progress
   */
  private async updateMemoryProgress(memoriesStored: number, totalMemories: number): Promise<void> {
    const progressPercentage = Math.round((memoriesStored / totalMemories) * 100);
    
    await prisma.personaGenerationJob.update({
      where: { id: this.jobId },
      data: {
        memoriesStored,
        memoryStorageProgress: progressPercentage,
      }
    });
  }
}
