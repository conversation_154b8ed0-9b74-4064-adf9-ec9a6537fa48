/**
 * Performance monitoring utilities for BuddyChip
 * Tracks query performance and provides optimization insights
 * Enhanced for refactoring validation and baseline tracking
 */

import { isFeatureEnabled, RefactorFlag } from "./feature-flags";

interface QueryMetrics {
  query: string;
  duration: number;
  timestamp: Date;
  params?: any;
  userId?: string;
  resultCount?: number;
}

/**
 * Performance baseline for refactoring validation
 */
interface PerformanceBaseline {
  metric: string;
  baseline: number;
  target: number;
  threshold: number;
  unit: string;
  description: string;
}

/**
 * Refactoring performance metrics
 */
interface RefactorMetric {
  type: string;
  value: number;
  timestamp: number;
  tags: Record<string, string>;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: QueryMetrics[] = [];
  private refactorMetrics: RefactorMetric[] = [];
  private baselines: Map<string, PerformanceBaseline> = new Map();
  private readonly MAX_METRICS = 1000; // Keep last 1000 queries
  private readonly MAX_REFACTOR_METRICS = 5000; // Keep more refactor metrics

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  constructor() {
    // Initialize default baselines for refactoring
    this.initializeBaselines();
  }

  /**
   * Initialize performance baselines for refactoring validation
   */
  private initializeBaselines(): void {
    const defaultBaselines: PerformanceBaseline[] = [
      {
        metric: "api_response_time",
        baseline: 500,
        target: 300,
        threshold: 1000,
        unit: "ms",
        description: "Average API response time",
      },
      {
        metric: "db_query_time",
        baseline: 100,
        target: 50,
        threshold: 500,
        unit: "ms",
        description: "Average database query time",
      },
      {
        metric: "component_render_time",
        baseline: 50,
        target: 30,
        threshold: 200,
        unit: "ms",
        description: "Average component render time",
      },
      {
        metric: "memory_usage",
        baseline: 100,
        target: 80,
        threshold: 200,
        unit: "MB",
        description: "Average memory usage",
      },
    ];

    defaultBaselines.forEach(baseline => {
      this.baselines.set(baseline.metric, baseline);
    });
  }

  /**
   * Track a database query with timing
   */
  async trackQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    params?: any,
    userId?: string
  ): Promise<T> {
    const startTime = performance.now();

    try {
      const result = await queryFn();
      const duration = performance.now() - startTime;

      // Determine result count if it's an array
      let resultCount: number | undefined;
      if (Array.isArray(result)) {
        resultCount = result.length;
      } else if (result && typeof result === "object" && "length" in result) {
        resultCount = (result as any).length;
      }

      this.recordMetric({
        query: queryName,
        duration,
        timestamp: new Date(),
        params,
        userId,
        resultCount,
      });

      // Log slow queries
      if (duration > 1000) {
        console.warn(
          `🐌 Slow query detected: ${queryName} took ${duration.toFixed(2)}ms`,
          {
            params,
            userId,
            resultCount,
          }
        );
      } else if (duration > 500) {
        console.info(
          `⚠️ Moderate query: ${queryName} took ${duration.toFixed(2)}ms`,
          {
            resultCount,
          }
        );
      } else {
        console.log(
          `⚡ Fast query: ${queryName} took ${duration.toFixed(2)}ms`,
          {
            resultCount,
          }
        );
      }

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      console.error(
        `❌ Query failed: ${queryName} failed after ${duration.toFixed(2)}ms`,
        {
          error: error instanceof Error ? error.message : String(error),
          params,
          userId,
        }
      );
      throw error;
    }
  }

  private recordMetric(metric: QueryMetrics): void {
    this.metrics.push(metric);

    // Keep only the last MAX_METRICS entries
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }
  }

  /**
   * Get performance statistics
   */
  getStats(minutes: number = 60): {
    averageDuration: number;
    slowQueries: QueryMetrics[];
    queryCount: number;
    topSlowQueries: Array<{
      query: string;
      avgDuration: number;
      count: number;
    }>;
  } {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000);
    const recentMetrics = this.metrics.filter((m) => m.timestamp > cutoff);

    if (recentMetrics.length === 0) {
      return {
        averageDuration: 0,
        slowQueries: [],
        queryCount: 0,
        topSlowQueries: [],
      };
    }

    const averageDuration =
      recentMetrics.reduce((sum, m) => sum + m.duration, 0) /
      recentMetrics.length;
    const slowQueries = recentMetrics.filter((m) => m.duration > 1000);

    // Group by query name and calculate averages
    const queryGroups = recentMetrics.reduce(
      (acc, metric) => {
        if (!acc[metric.query]) {
          acc[metric.query] = { durations: [], count: 0 };
        }
        acc[metric.query].durations.push(metric.duration);
        acc[metric.query].count++;
        return acc;
      },
      {} as Record<string, { durations: number[]; count: number }>
    );

    const topSlowQueries = Object.entries(queryGroups)
      .map(([query, data]) => ({
        query,
        avgDuration:
          data.durations.reduce((sum, d) => sum + d, 0) / data.durations.length,
        count: data.count,
      }))
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, 10);

    return {
      averageDuration,
      slowQueries,
      queryCount: recentMetrics.length,
      topSlowQueries,
    };
  }

  /**
   * Log performance summary
   */
  logSummary(minutes: number = 60): void {
    const stats = this.getStats(minutes);

    console.log(`📊 Performance Summary (last ${minutes} minutes):`);
    console.log(`   Total queries: ${stats.queryCount}`);
    console.log(`   Average duration: ${stats.averageDuration.toFixed(2)}ms`);
    console.log(`   Slow queries (>1s): ${stats.slowQueries.length}`);

    if (stats.topSlowQueries.length > 0) {
      console.log(`   Top slow queries:`);
      stats.topSlowQueries.slice(0, 5).forEach((query, index) => {
        console.log(
          `     ${index + 1}. ${query.query}: ${query.avgDuration.toFixed(2)}ms avg (${query.count} calls)`
        );
      });
    }
  }
}

// Add refactoring-specific methods to the existing class
PerformanceMonitor.prototype.recordRefactorMetric = function(
  type: string,
  value: number,
  tags: Record<string, string> = {},
  metadata?: Record<string, any>
): void {
  if (!isFeatureEnabled(RefactorFlag.ENABLE_PERFORMANCE_MONITORING)) {
    return;
  }

  const metric: RefactorMetric = {
    type,
    value,
    timestamp: Date.now(),
    tags: {
      environment: process.env.NODE_ENV || "development",
      version: process.env.npm_package_version || "unknown",
      ...tags,
    },
    metadata,
  };

  this.refactorMetrics.push(metric);

  // Keep only recent metrics
  if (this.refactorMetrics.length > this.MAX_REFACTOR_METRICS) {
    this.refactorMetrics = this.refactorMetrics.slice(-this.MAX_REFACTOR_METRICS);
  }

  // Log in development
  if (process.env.NODE_ENV === "development" && isFeatureEnabled(RefactorFlag.ENABLE_REFACTOR_LOGGING)) {
    console.log(`📊 Refactor metric: ${type} = ${value}`, tags);
  }
};

PerformanceMonitor.prototype.compareToBaseline = function(
  metricType: string,
  timeWindow: number = 300000
): {
  current: number;
  baseline: number;
  target: number;
  improvement: number;
  status: "better" | "worse" | "on_target";
} {
  const baseline = this.baselines.get(metricType);
  if (!baseline) {
    throw new Error(`No baseline found for metric type: ${metricType}`);
  }

  const cutoff = Date.now() - timeWindow;
  const recentMetrics = this.refactorMetrics
    .filter((m: RefactorMetric) => m.type === metricType && m.timestamp > cutoff)
    .map((m: RefactorMetric) => m.value);

  if (recentMetrics.length === 0) {
    throw new Error(`No recent metrics found for type: ${metricType}`);
  }

  const current = recentMetrics.reduce((sum, val) => sum + val, 0) / recentMetrics.length;
  const improvement = ((baseline.baseline - current) / baseline.baseline) * 100;

  let status: "better" | "worse" | "on_target";
  if (current <= baseline.target) {
    status = "on_target";
  } else if (current < baseline.baseline) {
    status = "better";
  } else {
    status = "worse";
  }

  return {
    current,
    baseline: baseline.baseline,
    target: baseline.target,
    improvement,
    status,
  };
};

PerformanceMonitor.prototype.getRefactorReport = function(): {
  summary: Record<string, any>;
  metrics: Record<string, any>;
  alerts: Array<{ metric: string; message: string; severity: string }>;
} {
  const summary = {
    totalRefactorMetrics: this.refactorMetrics.length,
    totalQueryMetrics: this.metrics.length,
    timeRange: {
      start: this.refactorMetrics.length > 0 ? Math.min(...this.refactorMetrics.map((m: RefactorMetric) => m.timestamp)) : 0,
      end: this.refactorMetrics.length > 0 ? Math.max(...this.refactorMetrics.map((m: RefactorMetric) => m.timestamp)) : 0,
    },
    monitoringEnabled: isFeatureEnabled(RefactorFlag.ENABLE_PERFORMANCE_MONITORING),
  };

  const metrics: Record<string, any> = {};
  const alerts: Array<{ metric: string; message: string; severity: string }> = [];

  for (const [type, baseline] of this.baselines) {
    try {
      const comparison = this.compareToBaseline(type);
      metrics[type] = {
        ...comparison,
        baseline: baseline,
      };

      // Check for alerts
      if (comparison.current > baseline.threshold) {
        alerts.push({
          metric: type,
          message: `${type} (${comparison.current.toFixed(2)}${baseline.unit}) exceeds threshold (${baseline.threshold}${baseline.unit})`,
          severity: "high",
        });
      } else if (comparison.status === "worse") {
        alerts.push({
          metric: type,
          message: `${type} performance degraded by ${Math.abs(comparison.improvement).toFixed(1)}%`,
          severity: "medium",
        });
      }
    } catch (error) {
      // Skip metrics with no data
      metrics[type] = {
        error: "No data available",
        baseline: baseline,
      };
    }
  }

  return { summary, metrics, alerts };
};

export const performanceMonitor = PerformanceMonitor.getInstance();

/**
 * Helper decorator for tracking Prisma queries
 */
export function trackPrismaQuery<T>(
  queryName: string,
  params?: any,
  userId?: string
) {
  return (queryFn: () => Promise<T>): Promise<T> => {
    return performanceMonitor.trackQuery(queryName, queryFn, params, userId);
  };
}
