/**
 * Database Optimization Service
 * 
 * Provides optimized query patterns, batch operations, and performance monitoring
 * for the BuddyChip application. Implements best practices for Prisma 6.9.0 and PostgreSQL.
 */

import { PrismaClient, type Prisma } from "../../prisma/generated";
import { performanceMonitor } from "./performance-monitor";
import { createPrismaClient } from "./prisma-config";

// Singleton optimized client
const globalForOptimizer = globalThis as unknown as {
  optimizedPrisma: PrismaClient | undefined;
};

export const optimizedPrisma = globalForOptimizer.optimizedPrisma ?? createPrismaClient({
  instanceId: "optimizer",
  forceQueryLogs: process.env.ENABLE_QUERY_OPTIMIZATION_LOGS === "true",
});

if (process.env.NODE_ENV !== "production") {
  globalForOptimizer.optimizedPrisma = optimizedPrisma;
}

/**
 * Batch operation utilities for improved performance
 */
export class BatchOperations {
  private static readonly DEFAULT_BATCH_SIZE = 100;
  private static readonly DEFAULT_DELAY_MS = 10;

  /**
   * Batch create operations with automatic chunking
   */
  static async batchCreate<T extends Record<string, any>>(
    model: string,
    data: T[],
    options: {
      batchSize?: number;
      delayMs?: number;
      skipDuplicates?: boolean;
    } = {}
  ): Promise<{ count: number; batches: number }> {
    const {
      batchSize = this.DEFAULT_BATCH_SIZE,
      delayMs = this.DEFAULT_DELAY_MS,
      skipDuplicates = true,
    } = options;

    console.log(`🔄 BatchOperations: Creating ${data.length} ${model} records in batches of ${batchSize}`);

    let totalCreated = 0;
    let batchCount = 0;

    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      
      try {
        const result = await performanceMonitor.trackQuery(
          `batch.create.${model}`,
          () => (optimizedPrisma as any)[model].createMany({
            data: batch,
            skipDuplicates,
          })
        );

        totalCreated += result.count;
        batchCount++;

        console.log(`✅ BatchOperations: Batch ${batchCount} completed - ${result.count} ${model} records created`);

        // Add delay between batches to prevent overwhelming the database
        if (delayMs > 0 && i + batchSize < data.length) {
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
      } catch (error) {
        console.error(`❌ BatchOperations: Batch ${batchCount + 1} failed:`, error);
        throw error;
      }
    }

    console.log(`🎉 BatchOperations: Completed ${batchCount} batches, ${totalCreated} total ${model} records created`);
    return { count: totalCreated, batches: batchCount };
  }

  /**
   * Batch update operations with optimized queries
   */
  static async batchUpdate<T extends Record<string, any>>(
    model: string,
    updates: Array<{ where: any; data: T }>,
    options: {
      batchSize?: number;
      delayMs?: number;
    } = {}
  ): Promise<{ count: number; batches: number }> {
    const {
      batchSize = this.DEFAULT_BATCH_SIZE,
      delayMs = this.DEFAULT_DELAY_MS,
    } = options;

    console.log(`🔄 BatchOperations: Updating ${updates.length} ${model} records in batches of ${batchSize}`);

    let totalUpdated = 0;
    let batchCount = 0;

    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      try {
        // Use transaction for batch updates
        const results = await performanceMonitor.trackQuery(
          `batch.update.${model}`,
          () => optimizedPrisma.$transaction(
            batch.map(({ where, data }) => 
              (optimizedPrisma as any)[model].updateMany({
                where,
                data,
              })
            )
          )
        );

        const batchUpdated = results.reduce((sum, result) => sum + result.count, 0);
        totalUpdated += batchUpdated;
        batchCount++;

        console.log(`✅ BatchOperations: Batch ${batchCount} completed - ${batchUpdated} ${model} records updated`);

        if (delayMs > 0 && i + batchSize < updates.length) {
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
      } catch (error) {
        console.error(`❌ BatchOperations: Update batch ${batchCount + 1} failed:`, error);
        throw error;
      }
    }

    console.log(`🎉 BatchOperations: Completed ${batchCount} batches, ${totalUpdated} total ${model} records updated`);
    return { count: totalUpdated, batches: batchCount };
  }

  /**
   * Batch delete operations with safety checks
   */
  static async batchDelete(
    model: string,
    whereConditions: any[],
    options: {
      batchSize?: number;
      delayMs?: number;
      dryRun?: boolean;
    } = {}
  ): Promise<{ count: number; batches: number }> {
    const {
      batchSize = this.DEFAULT_BATCH_SIZE,
      delayMs = this.DEFAULT_DELAY_MS,
      dryRun = false,
    } = options;

    if (dryRun) {
      console.log(`🔍 BatchOperations: DRY RUN - Would delete ${whereConditions.length} ${model} records`);
      return { count: 0, batches: 0 };
    }

    console.log(`🗑️ BatchOperations: Deleting ${whereConditions.length} ${model} records in batches of ${batchSize}`);

    let totalDeleted = 0;
    let batchCount = 0;

    for (let i = 0; i < whereConditions.length; i += batchSize) {
      const batch = whereConditions.slice(i, i + batchSize);
      
      try {
        const results = await performanceMonitor.trackQuery(
          `batch.delete.${model}`,
          () => optimizedPrisma.$transaction(
            batch.map(where => 
              (optimizedPrisma as any)[model].deleteMany({ where })
            )
          )
        );

        const batchDeleted = results.reduce((sum, result) => sum + result.count, 0);
        totalDeleted += batchDeleted;
        batchCount++;

        console.log(`✅ BatchOperations: Batch ${batchCount} completed - ${batchDeleted} ${model} records deleted`);

        if (delayMs > 0 && i + batchSize < whereConditions.length) {
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
      } catch (error) {
        console.error(`❌ BatchOperations: Delete batch ${batchCount + 1} failed:`, error);
        throw error;
      }
    }

    console.log(`🎉 BatchOperations: Completed ${batchCount} batches, ${totalDeleted} total ${model} records deleted`);
    return { count: totalDeleted, batches: batchCount };
  }
}

/**
 * Optimized query patterns for common operations
 */
export class OptimizedQueries {
  /**
   * Get mentions with optimized includes and pagination
   */
  static async getMentionsOptimized(
    userId: string,
    options: {
      limit?: number;
      cursor?: string;
      includeResponses?: boolean;
      includeAccount?: boolean;
      archived?: boolean;
    } = {}
  ) {
    const {
      limit = 20,
      cursor,
      includeResponses = true,
      includeAccount = true,
      archived = false,
    } = options;

    const where: Prisma.MentionWhereInput = {
      userId,
      archived,
    };

    if (cursor) {
      where.id = { lt: cursor };
    }

    return performanceMonitor.trackQuery(
      "mentions.getOptimized",
      () => optimizedPrisma.mention.findMany({
        where,
        take: limit,
        orderBy: [
          { mentionedAt: "desc" },
          { id: "desc" }, // Secondary sort for consistent pagination
        ],
        select: {
          id: true,
          content: true,
          authorName: true,
          authorHandle: true,
          authorAvatarUrl: true,
          link: true,
          mentionedAt: true,
          createdAt: true,
          bullishScore: true,
          importanceScore: true,
          keywords: true,
          processed: true,
          ...(includeAccount && {
            account: {
              select: {
                id: true,
                twitterHandle: true,
                displayName: true,
                avatarUrl: true,
                isActive: true,
              },
            },
          }),
          ...(includeResponses && {
            responses: {
              select: {
                id: true,
                content: true,
                createdAt: true,
              },
              orderBy: { createdAt: "desc" },
              take: 1, // Only latest response
            },
          }),
        },
      }),
      { userId, limit, cursor, includeResponses, includeAccount }
    );
  }

  /**
   * Get user with optimized plan and usage data
   */
  static async getUserWithPlanOptimized(userId: string) {
    return performanceMonitor.trackQuery(
      "user.getWithPlan",
      () => optimizedPrisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          avatarUrl: true,
          planId: true,
          personalityId: true,
          customSystemPrompt: true,
          useFirstPerson: true,
          createdAt: true,
          lastActiveAt: true,
          plan: {
            select: {
              id: true,
              name: true,
              price: true,
              features: {
                select: {
                  feature: true,
                  limit: true,
                },
              },
            },
          },
          personality: {
            select: {
              id: true,
              name: true,
              systemPrompt: true,
            },
          },
          // Get current billing period usage
          usageLogs: {
            where: {
              billingPeriod: {
                gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
              },
            },
            select: {
              feature: true,
              amount: true,
            },
          },
        },
      }),
      { userId }
    );
  }

  /**
   * Get account mentions with efficient pagination
   */
  static async getAccountMentionsOptimized(
    accountId: string,
    options: {
      limit?: number;
      cursor?: string;
      includeResponses?: boolean;
    } = {}
  ) {
    const { limit = 50, cursor, includeResponses = false } = options;

    const where: Prisma.MentionWhereInput = {
      accountId,
      archived: false,
    };

    if (cursor) {
      // Use compound cursor for better performance
      const [timestamp, id] = cursor.split('_');
      where.OR = [
        { mentionedAt: { lt: new Date(timestamp) } },
        { 
          mentionedAt: new Date(timestamp),
          id: { lt: id }
        }
      ];
    }

    return performanceMonitor.trackQuery(
      "mentions.getByAccount",
      () => optimizedPrisma.mention.findMany({
        where,
        take: limit,
        orderBy: [
          { mentionedAt: "desc" },
          { id: "desc" },
        ],
        select: {
          id: true,
          content: true,
          authorName: true,
          authorHandle: true,
          authorAvatarUrl: true,
          link: true,
          mentionedAt: true,
          bullishScore: true,
          processed: true,
          ...(includeResponses && {
            responses: {
              select: {
                id: true,
                content: true,
                createdAt: true,
              },
              orderBy: { createdAt: "desc" },
              take: 1,
            },
          }),
        },
      }),
      { accountId, limit, cursor }
    );
  }
}

/**
 * Database health and optimization monitoring
 */
export class DatabaseHealth {
  /**
   * Check database performance metrics
   */
  static async checkPerformanceMetrics() {
    const metrics = {
      connectionCount: 0,
      slowQueries: 0,
      indexUsage: {},
      tableStats: {},
    };

    try {
      // Check active connections
      const connections = await optimizedPrisma.$queryRaw<Array<{ count: number }>>`
        SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'
      `;
      metrics.connectionCount = Number(connections[0]?.count || 0);

      // Check for slow queries (queries taking > 1 second)
      const slowQueries = await optimizedPrisma.$queryRaw<Array<{ count: number }>>`
        SELECT count(*) as count 
        FROM pg_stat_statements 
        WHERE mean_exec_time > 1000
      `;
      metrics.slowQueries = Number(slowQueries[0]?.count || 0);

      console.log("📊 Database Health Metrics:", metrics);
      return metrics;
    } catch (error) {
      console.error("❌ Failed to check database health:", error);
      return metrics;
    }
  }

  /**
   * Analyze table statistics for optimization opportunities
   */
  static async analyzeTableStats(tableName?: string) {
    try {
      const query = tableName 
        ? `SELECT * FROM pg_stat_user_tables WHERE relname = '${tableName}'`
        : `SELECT * FROM pg_stat_user_tables ORDER BY seq_scan DESC LIMIT 10`;

      const stats = await optimizedPrisma.$queryRawUnsafe(query);
      
      console.log(`📈 Table Statistics${tableName ? ` for ${tableName}` : ' (Top 10 by sequential scans)'}:`, stats);
      return stats;
    } catch (error) {
      console.error("❌ Failed to analyze table stats:", error);
      return [];
    }
  }

  /**
   * Suggest index optimizations based on query patterns
   */
  static async suggestIndexOptimizations() {
    try {
      // Find tables with high sequential scan ratios
      const suggestions = await optimizedPrisma.$queryRaw<Array<{
        table_name: string;
        seq_scan: number;
        idx_scan: number;
        ratio: number;
      }>>`
        SELECT 
          relname as table_name,
          seq_scan,
          idx_scan,
          CASE 
            WHEN seq_scan + idx_scan > 0 
            THEN ROUND((seq_scan::float / (seq_scan + idx_scan)) * 100, 2)
            ELSE 0 
          END as ratio
        FROM pg_stat_user_tables 
        WHERE seq_scan + idx_scan > 100
        ORDER BY ratio DESC
        LIMIT 10
      `;

      console.log("💡 Index Optimization Suggestions:", suggestions);
      return suggestions;
    } catch (error) {
      console.error("❌ Failed to generate index suggestions:", error);
      return [];
    }
  }
}

/**
 * Export all optimization utilities
 */
export const databaseOptimizer = {
  batch: BatchOperations,
  queries: OptimizedQueries,
  health: DatabaseHealth,
  client: optimizedPrisma,
};
