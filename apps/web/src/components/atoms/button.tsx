"use client";
import type React from "react";
import type { IconType } from "react-icons";
import { MdArrowOutward } from "react-icons/md";
import { Button, type ButtonProps as BaseButtonProps } from "../ui/button";
import IconButton from "./icon-button";

interface ButtonProps extends Omit<BaseButtonProps, "children" | "icon"> {
  children: React.ReactNode;
  icon?: IconType;
  iconVariant?: "primary" | "secondary" | "tertiary";
}

const PrimaryButton = ({
  icon,
  children,
  className,
  variant = "primary",
  size = "primary",
  iconVariant = "primary",
  disabled = false,
  onClick,
  ...props
}: ButtonProps) => {
  const getIconVariant = (variant: string) => {
    switch (variant) {
      case "primary": return "appSecondary";
      case "secondary": return "appSecondary";
      case "tertiary": return "appTertiary";
      default: return "appSecondary";
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      disabled={disabled}
      onClick={onClick}
      {...props}
    >
      <span className="flex-grow text-left leading-tight">{children}</span>
      {icon && (
        <span className="transition-all duration-300 flex-shrink-0 ml-2 sm:ml-4">
          <IconButton
            icon={MdArrowOutward}
            className="w-[36px] h-[36px] sm:w-[44px] sm:h-[44px] md:w-[70px] md:h-[70px] overflow-hidden"
            variant={getIconVariant(iconVariant)}
            asChild={true}
          />
        </span>
      )}
    </Button>
  );
};

export default PrimaryButton;
