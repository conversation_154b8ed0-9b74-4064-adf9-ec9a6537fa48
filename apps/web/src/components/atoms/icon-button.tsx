"use client";
import { forwardRef } from "react";
import type { IconType } from "react-icons";
import { Button, type ButtonProps } from "../ui/button";
import { cn } from "@/lib/utils";

interface IconButtonProps extends Omit<ButtonProps, "children" | "icon"> {
  icon?: IconType | React.ReactNode | null;
  iconClassName?: string;
  "aria-label"?: string;
}

const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(({
  icon = null,
  className,
  variant = "appSecondary",
  size = "icon",
  iconClassName,
  "aria-label": ariaLabel,
  ...props
}, ref) => {
  const Icon = typeof icon === "function" ? icon : null;

  return (
    <Button
      ref={ref}
      variant={variant}
      size={size}
      className={cn("rounded-full", className)}
      aria-label={ariaLabel}
      {...props}
    >
      {Icon && <Icon className={cn("w-[28px] h-[28px]", iconClassName)} />}
      {typeof icon !== "function" && icon}
    </Button>
  );
});

IconButton.displayName = "IconButton";

export default IconButton;
