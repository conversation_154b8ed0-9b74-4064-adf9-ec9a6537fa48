"use client";

import { ConfirmationModal } from "./modal";

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  showDontShowAgain?: boolean;
  dontShowAgainChecked?: boolean;
  onDontShowAgainChange?: (checked: boolean) => void;
}

export default function ConfirmationDialog(props: ConfirmationDialogProps) {
  return <ConfirmationModal {...props} />;
}
