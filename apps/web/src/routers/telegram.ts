/**
 * Telegram tRPC Router
 *
 * Handles Telegram bot integration operations
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import {
  checkTelegramUserAccess,
  generateTelegramLinkCode,
  getLinkedTelegramAccount,
  linkTelegramAccount,
  unlinkTelegramAccount,
} from "../lib/telegram-auth";
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";

export const telegramRouter = createTRPCRouter({
  /**
   * Get linked Telegram account information
   */
  getLinkedAccount: protectedProcedure.query(async ({ ctx }) => {
    try {
      console.log(
        "📋 Telegram tRPC: Getting linked account for user:",
        ctx.userId
      );

      const telegramUser = await getLinkedTelegramAccount(ctx.userId);

      if (telegramUser) {
        return {
          linked: true,
          telegramUser: {
            id: telegramUser.id,
            username: telegram<PERSON>ser.username,
            firstName: telegramUser.firstName,
            lastName: telegramUser.lastName,
            isActive: telegramUser.isActive,
            lastActiveAt: telegramUser.lastActiveAt,
            createdAt: telegramUser.createdAt,
          },
        };
      }

      return {
        linked: false,
        telegramUser: null,
      };
    } catch (error) {
      console.error("❌ Telegram tRPC: Error getting linked account:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to retrieve Telegram account information",
      });
    }
  }),

  /**
   * Generate a link code for account linking
   */
  generateLinkCode: protectedProcedure
    .input(
      z.object({
        telegramChatId: z.string().min(1, "Telegram chat ID is required"),
      })
    )
    .mutation(async ({ input }) => {
      try {
        console.log(
          "🔗 Telegram tRPC: Generating link code for chat:",
          input.telegramChatId
        );

        const linkCode = generateTelegramLinkCode(input.telegramChatId);

        return {
          success: true,
          linkCode: linkCode.code,
          expiresAt: linkCode.expiresAt,
          message: "Link code generated successfully",
        };
      } catch (error) {
        console.error("❌ Telegram tRPC: Error generating link code:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate link code",
        });
      }
    }),

  /**
   * Link Telegram account to current user
   */
  linkAccount: protectedProcedure
    .input(
      z.object({
        linkCode: z.string().min(1, "Link code is required"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        console.log("🔗 Telegram tRPC: Linking account for user:", ctx.userId);

        const result = await linkTelegramAccount(ctx.userId, input.linkCode);

        if (result.success) {
          return {
            success: true,
            message: result.message,
            telegramUser: result.telegramUser,
          };
        } else {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: result.message,
          });
        }
      } catch (error) {
        console.error("❌ Telegram tRPC: Error linking account:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to link Telegram account",
        });
      }
    }),

  /**
   * Unlink Telegram account from current user
   */
  unlinkAccount: protectedProcedure
    .input(
      z.object({
        confirm: z
          .boolean()
          .refine((val) => val === true, "Confirmation required"),
      })
    )
    .mutation(async ({ ctx }) => {
      try {
        console.log(
          "🔓 Telegram tRPC: Unlinking account for user:",
          ctx.userId
        );

        const result = await unlinkTelegramAccount(ctx.userId);

        if (result.success) {
          return {
            success: true,
            message: result.message,
          };
        } else {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: result.message,
          });
        }
      } catch (error) {
        console.error("❌ Telegram tRPC: Error unlinking account:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to unlink Telegram account",
        });
      }
    }),

  /**
   * Check Telegram user access and permissions
   */
  checkUserAccess: protectedProcedure
    .input(
      z.object({
        telegramChatId: z.string().min(1, "Telegram chat ID is required"),
      })
    )
    .query(async ({ input }) => {
      try {
        console.log(
          "🔍 Telegram tRPC: Checking user access for chat:",
          input.telegramChatId
        );

        const access = await checkTelegramUserAccess(input.telegramChatId);

        return {
          hasAccess: access.hasAccess,
          plan: access.plan,
          userId: access.userId,
        };
      } catch (error) {
        console.error("❌ Telegram tRPC: Error checking user access:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check user access",
        });
      }
    }),

  /**
   * Get Telegram bot information and status
   */
  getBotInfo: protectedProcedure.query(async () => {
    try {
      const botToken = process.env.TELEGRAM_BOT_TOKEN;
      const webhookUrl = `${process.env.NEXT_PUBLIC_APP_URL || "https://buddychip.app"}/api/telegram/webhook`;

      return {
        configured: !!botToken,
        webhookUrl,
        features: {
          twitterIntegration: true,
          aiConversation: true,
          imageGeneration: true,
          webSearch: true,
        },
        commands: [
          { command: "/start", description: "Start using the bot" },
          { command: "/help", description: "Show help information" },
          { command: "/settings", description: "Manage account settings" },
          { command: "/status", description: "Check account status and usage" },
          { command: "/create", description: "Generate original social media posts" },
          { command: "/image", description: "Generate AI images with detailed prompts" },
        ],
      };
    } catch (error) {
      console.error("❌ Telegram tRPC: Error getting bot info:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to retrieve bot information",
      });
    }
  }),

  /**
   * Test Telegram integration
   */
  testIntegration: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      console.log(
        "🧪 Telegram tRPC: Testing integration for user:",
        ctx.userId
      );

      // Check if user has linked Telegram account
      const telegramUser = await getLinkedTelegramAccount(ctx.userId);

      if (!telegramUser) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "No linked Telegram account found. Please link your account first.",
        });
      }

      // In a real implementation, you might send a test message to the user
      // For now, just return success with account info

      return {
        success: true,
        message: "Telegram integration is working correctly",
        telegramUser: {
          username: telegramUser.username,
          firstName: telegramUser.firstName,
          isActive: telegramUser.isActive,
          lastActiveAt: telegramUser.lastActiveAt,
        },
      };
    } catch (error) {
      console.error("❌ Telegram tRPC: Error testing integration:", error);

      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to test Telegram integration",
      });
    }
  }),
});
